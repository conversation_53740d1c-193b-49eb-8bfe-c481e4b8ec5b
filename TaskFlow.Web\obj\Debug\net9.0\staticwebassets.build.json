{"Version": 1, "Hash": "cOLOThQM47NQg120yue3gtq82domw5dJmNOOfxOC+CQ=", "Source": "TaskFlow.Web", "BasePath": "_content/TaskFlow.Web", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "TaskFlow.Web\\wwwroot", "Source": "TaskFlow.Web", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "Pattern": "**"}], "Assets": [{"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\11y2nf4j36-c63t5i9ira.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=c63t5i9ira}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n2nwmiwapx", "Integrity": "zxq4fMIq5J99E6EK1H7qVe10DUZ2GcXauY+j6URB7DI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\1ctw0o17fe-aak5eirmym.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=aak5eirmym}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lq50o6u4le", "Integrity": "8xIvF19Nlu+E+UlJdrkdtyfj4s3Cs5JfKiPF/L5QK0c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\1ork37jani-c2oey78nd0.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\1sqahdjw2w-knl5pustf3.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=knl5pustf3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2ojh1hjsz", "Integrity": "nCVT1+BjbwsRwzOPpN1ZSa/iEC2jTv4WkLCtAvNJhLY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\22yenftene-fvhpjtyr6v.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\2cc7g8dnkc-ee0r1s7dh0.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\3uw1dz2yv7-j5mq2jizvt.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\5gjnp28z46-qesaa3a1fm.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=qesaa3a1fm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dtwnk525ov", "Integrity": "H9IPXBzUtJGG7PJdjNTz1WD7YcFYCi+22bHkHPR+CdY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\5uu7ycx7eu-6wam8sj21o.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "app#[.{fingerprint=6wam8sj21o}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xe2s0v5zy2", "Integrity": "xxiqA3yGYGiMCcyDlArcfhUmX1F6WqHu42fpwkjflCo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\app.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\6e4f3aw9u8-v0zj4ognzu.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\6f8bul21g6-iug4zs40ca.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=iug4zs40ca}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "47i7fz1mfu", "Integrity": "4MKp1Hk7iiMTqFAZoVE172eMhUFb9P2egSWFw3YPP4k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\8a0aj51wdw-rxsg74s51o.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rxsg74s51o}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xmcjt9e9cy", "Integrity": "ueerZxLdSKIEsWJ13wzBfTcRfd3uGSfOQwhUfM1jRec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\9g9fksc2vy-ausgxo2sd3.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\bkl21n1ixh-keugtjm085.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=keugtjm085}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q8bg8bu1pt", "Integrity": "Fi8DMQpQ0pHVk8uDPzXmz7v1U9ElGm/J/lTUl54WMHw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\c1xriqn00w-ynyaa8k90p.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=ynyaa8k90p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0uw774o5yt", "Integrity": "CKd1DZe0tmjYGXJCdpcyExOT7GvFEZKGtk3+Ij7ZTSc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\c6qmdw4ri0-zub09dkrxp.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=zub09dkrxp}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nqh38a62u8", "Integrity": "j8xOcokVqVMlkhtMkAD+dCGjp8eCK+RXUPREaOqe2TU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\cq5p1v8hv9-sebe1i1n8x.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=sebe1i1n8x}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xufunq36oh", "Integrity": "N4UpPPH7dwqNn708OBuX/liy3v1uuPjjyR9ELovuEYw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\docid0nk50-t1cqhe9u97.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=t1cqhe9u97}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7trewrvtze", "Integrity": "td/WnErsJHKP3LT8Bz6znCdABuCWnP513FYSQpHRK9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\e55pnj8i5z-sejl45xvog.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=sejl45xvog}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mhey2fgxm6", "Integrity": "2psAeYsQwIU+4hjhaa7aiuEI8eM0VqXG4mLMI2LB5kg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ekatybltws-xvp3kq03qx.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=xvp3kq03qx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1xcrflemqm", "Integrity": "Jb3hghPX8HIvLINQ2LRDLH33mFfqyzbk+nTsbiHtKr4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\fcr8eb906x-q9ht133ko3.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=q9ht133ko3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5fijz6kmv0", "Integrity": "AX9UjSDaqolhmzTa/KUDWMjLF2XlYT6I6JZuUGrm1nw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\gzxir09ed5-cosvhxvwiu.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\iyscw6kjtk-hrwsygsryq.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\jsy329rgjt-nvvlpmu67g.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\krrntovrdp-jd9uben2k1.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ktuithl01y-pj5nd1wqec.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\lqmvts9dba-xxsbxg4q4i.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=xxsbxg4q4i}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pakrqzmxkf", "Integrity": "gQXErDzWkQZSfjEczQ6fcQDBKpT0yn/vH7j1YSexA0c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\nr8pparx2w-m8m04ow07k.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=m8m04ow07k}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aqxwsrc1l2", "Integrity": "PqV7FoSrUvHZIAI97OB5Du66Rix7At2iTuksxeoo5Gk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\nuxbc26o8n-gye83jo8yx.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=gye83jo8yx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k64ed53csw", "Integrity": "VGKPtySribew+qZrTcy+fpnTi7Q3gxSv+HWQQ/4rQos=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\o0dcj0py4z-khv3du26ro.gz", "SourceId": "TaskFlow.Web", "SourceType": "Computed", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "TaskFlow.Web#[.{fingerprint=khv3du26ro}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\TaskFlow.Web.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p4nf60hzjh", "Integrity": "2bj+yQE2YI1QzLnFdJ8MIsiCxthylp2AwB1vfqLMHdY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\TaskFlow.Web.styles.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\oac7z3pbsy-1jo1kezdci.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=1jo1kezdci}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "75eywz3tll", "Integrity": "G3hEVXOXq2YI9Z7/yvAQvvf6j7gWnCvdHiIIKSi826M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ov6srq43h2-c2jlpeoesf.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ovchpumm0y-wl58j5mj3v.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=wl58j5mj3v}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nv0k9gv8u2", "Integrity": "cxoqFtEDkm4KCYDrmWgnE7zkiXwSKUlq/rctM76ouDg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\pybm8mc09z-d4r6k3f320.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=d4r6k3f320}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42ih0tfzwc", "Integrity": "rLnVjI3ToFgRadgC+qnQqkjBbeGzyUk/W1M+MRurfiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\qbt2gjo1ua-l53j0jwsua.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=l53j0jwsua}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "eiby6u36af", "Integrity": "xhw67ZJiHnM6w7VhJZ0xa4j4CdRyJh0f/YiG0s0T63k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\sbc2oexvxn-u3orf5n9fb.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=u3orf5n9fb}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c5scpwdb0k", "Integrity": "gOJ+qyInHxVGQAHaRs9ggiXfugMTsR/8thKNQLja68A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\t9yymp5818-khv3du26ro.gz", "SourceId": "TaskFlow.Web", "SourceType": "Computed", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "TaskFlow.Web#[.{fingerprint=khv3du26ro}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\TaskFlow.Web.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p4nf60hzjh", "Integrity": "2bj+yQE2YI1QzLnFdJ8MIsiCxthylp2AwB1vfqLMHdY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\TaskFlow.Web.bundle.scp.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\v6x955lyjj-r4e9w2rdcm.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\vkm14ixwgx-22vffe00uq.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=22vffe00uq}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mxqr4d2em4", "Integrity": "Gjkg0hmhA9Aga0f2ZS8ZqJ/GES4kxIvtWTfabr1bc2Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\vq2fdwhkxj-07r8e49zdp.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=07r8e49zdp}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "93kkzvrplm", "Integrity": "S45O8CWj18jAuvMjr7tniBMZLZx6s1gfCjeu1uXTsjQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\vz1dxm0tda-tmc1g35s3z.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=tmc1g35s3z}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7jljgo6o9r", "Integrity": "w8PM3Ms+TEJVQ/PGIsRL/kn0h83Pm5ek8Sm7/zkgCYc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\w8qngq9ie1-sybl3t77fa.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=sybl3t77fa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "txlefgy9aj", "Integrity": "kXW7yFoYyoJuZ8DvEVLMROrQ/SMEw2Pbr7LLiGIZlCY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\wrdhmnlm9b-aexeepp0ev.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\xv7sgvmssy-43atpzeawx.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=43atpzeawx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cmcmkw9yu7", "Integrity": "ZA1L2PX1u6VKIuxhJUgeKELAZwIreHb7fDdqlTEZyKs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ys21i01zgw-fsbi9cje9m.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\zgyeaunyzm-xy033osspr.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=xy033osspr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gwmucbd1n8", "Integrity": "kIioyvmf/tLdemeqRPWlzFgcfoZ1Ove/FdckmYDBXpk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\zt8myftf3o-ft3s53vfgj.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\TaskFlow.Web.styles.css", "SourceId": "TaskFlow.Web", "SourceType": "Computed", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "TaskFlow.Web#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "khv3du26ro", "Integrity": "KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\TaskFlow.Web.styles.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\TaskFlow.Web.bundle.scp.css", "SourceId": "TaskFlow.Web", "SourceType": "Computed", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "TaskFlow.Web#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "khv3du26ro", "Integrity": "KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\TaskFlow.Web.bundle.scp.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\app.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6wam8sj21o", "Integrity": "Wndt+V4Ynjm1ybKbGOiJLvIuWDuYYgNu3Cu400cBtzM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\favicon.png", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "t1cqhe9u97", "Integrity": "f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sejl45xvog", "Integrity": "hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xvp3kq03qx", "Integrity": "M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "22vffe00uq", "Integrity": "+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qesaa3a1fm", "Integrity": "rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tmc1g35s3z", "Integrity": "y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rxsg74s51o", "Integrity": "gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "q9ht133ko3", "Integrity": "UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gye83jo8yx", "Integrity": "uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wl58j5mj3v", "Integrity": "ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "d4r6k3f320", "Integrity": "MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "keugtjm085", "Integrity": "7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zub09dkrxp", "Integrity": "CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "43atpzeawx", "Integrity": "sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ynyaa8k90p", "Integrity": "/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c63t5i9ira", "Integrity": "rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "aak5eirmym", "Integrity": "DoRbpT+MFNf8Kw514KCM7HP0Xkm7mSIlvI2H77dfRDA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "m8m04ow07k", "Integrity": "llRPAjsTCQdX9I6jAgtmhr1rUT9tvAVdaPQrYD38Td4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sebe1i1n8x", "Integrity": "RCqG8kOYgTkEN6lTuil29qaURdLpMdgr2x1tKpEn2Ow=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "07r8e49zdp", "Integrity": "rOniCgnoNJIVZp7PCJnzSGta8mgTGweOpxp/z4+Oc+k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xy033osspr", "Integrity": "V9VHrfWTQdDZhpDjgAzkkk7JIEn/cx3lMtjiDUXul/Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sybl3t77fa", "Integrity": "1NZ0e0qqYzJigO5XFb9Y2bgUwRxi1f/jpwe4eU6A5pA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "l53j0jwsua", "Integrity": "Rz2BsZTYP8L0/pkMgZIAMFzaFfsMWc+8175AqV69XCE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "u3orf5n9fb", "Integrity": "ygX//9b5IsEzbh1wXO6jfbN5R0YVHtui5Zm2Pxzfows=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1jo1kezdci", "Integrity": "k9Hcnfw1uBvIz3LBQJE2enuHAQ+2vcGQx7FY/sjMCT0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xxsbxg4q4i", "Integrity": "u9e0mnJh0mj/d32Mx6N9Ibj/0+LtjionqG5TWUyotPk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "iug4zs40ca", "Integrity": "tp7hlG6xiG3KkONtF2Z5esr5FTcW2r83xX3hex8WMX8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js"}, {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "knl5pustf3", "Integrity": "IBIemhxt8rrB7DVR6DoN0KwZECFkw69mvMSn40GJXsc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map"}], "Endpoints": [{"Route": "app.6wam8sj21o.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\5uu7ycx7eu-6wam8sj21o.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000644329897"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1551"}, {"Name": "ETag", "Value": "\"xxiqA3yGYGiMCcyDlArcfhUmX1F6WqHu42fpwkjflCo=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"Wndt+V4Ynjm1ybKbGOiJLvIuWDuYYgNu3Cu400cBtzM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6wam8sj21o"}, {"Name": "label", "Value": "app.css"}, {"Name": "integrity", "Value": "sha256-Wndt+V4Ynjm1ybKbGOiJLvIuWDuYYgNu3Cu400cBtzM="}]}, {"Route": "app.6wam8sj21o.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2825"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Wndt+V4Ynjm1ybKbGOiJLvIuWDuYYgNu3Cu400cBtzM=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6wam8sj21o"}, {"Name": "label", "Value": "app.css"}, {"Name": "integrity", "Value": "sha256-Wndt+V4Ynjm1ybKbGOiJLvIuWDuYYgNu3Cu400cBtzM="}]}, {"Route": "app.6wam8sj21o.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\5uu7ycx7eu-6wam8sj21o.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1551"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xxiqA3yGYGiMCcyDlArcfhUmX1F6WqHu42fpwkjflCo=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6wam8sj21o"}, {"Name": "label", "Value": "app.css.gz"}, {"Name": "integrity", "Value": "sha256-xxiqA3yGYGiMCcyDlArcfhUmX1F6WqHu42fpwkjflCo="}]}, {"Route": "app.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\5uu7ycx7eu-6wam8sj21o.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000644329897"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1551"}, {"Name": "ETag", "Value": "\"xxiqA3yGYGiMCcyDlArcfhUmX1F6WqHu42fpwkjflCo=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"Wndt+V4Ynjm1ybKbGOiJLvIuWDuYYgNu3Cu400cBtzM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Wndt+V4Ynjm1ybKbGOiJLvIuWDuYYgNu3Cu400cBtzM="}]}, {"Route": "app.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2825"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Wndt+V4Ynjm1ybKbGOiJLvIuWDuYYgNu3Cu400cBtzM=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Wndt+V4Ynjm1ybKbGOiJLvIuWDuYYgNu3Cu400cBtzM="}]}, {"Route": "app.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\5uu7ycx7eu-6wam8sj21o.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1551"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xxiqA3yGYGiMCcyDlArcfhUmX1F6WqHu42fpwkjflCo=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xxiqA3yGYGiMCcyDlArcfhUmX1F6WqHu42fpwkjflCo="}]}, {"Route": "favicon.ifv42okdf2.png", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ifv42okdf2"}, {"Name": "label", "Value": "favicon.png"}, {"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "favicon.png", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\docid0nk50-t1cqhe9u97.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148038490"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6754"}, {"Name": "ETag", "Value": "\"td/WnErsJHKP3LT8Bz6znCdABuCWnP513FYSQpHRK9A=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74413"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.c2jlpeoesf.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ov6srq43h2-c2jlpeoesf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030492453"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32794"}, {"Name": "ETag", "Value": "\"ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2j<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}, {"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.c2jlpeoesf.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "203221"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2j<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}, {"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.c2jlpeoesf.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ov6srq43h2-c2jlpeoesf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32794"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2j<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz"}, {"Name": "integrity", "Value": "sha256-ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\docid0nk50-t1cqhe9u97.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6754"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"td/WnErsJHKP3LT8Bz6znCdABuCWnP513FYSQpHRK9A=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-td/WnErsJHKP3LT8Bz6znCdABuCWnP513FYSQpHRK9A="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ov6srq43h2-c2jlpeoesf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030492453"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32794"}, {"Name": "ETag", "Value": "\"ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "203221"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ov6srq43h2-c2jlpeoesf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32794"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\e55pnj8i5z-sejl45xvog.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167392032"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5973"}, {"Name": "ETag", "Value": "\"2psAeYsQwIU+4hjhaa7aiuEI8eM0VqXG4mLMI2LB5kg=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51800"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.aexeepp0ev.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\wrdhmnlm9b-aexeepp0ev.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072421784"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13807"}, {"Name": "ETag", "Value": "\"oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aexeepp0ev"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, {"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.aexeepp0ev.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "115986"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aexeepp0ev"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, {"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.aexeepp0ev.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\wrdhmnlm9b-aexeepp0ev.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aexeepp0ev"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\e55pnj8i5z-sejl45xvog.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5973"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2psAeYsQwIU+4hjhaa7aiuEI8eM0VqXG4mLMI2LB5kg=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2psAeYsQwIU+4hjhaa7aiuEI8eM0VqXG4mLMI2LB5kg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\wrdhmnlm9b-aexeepp0ev.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072421784"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13807"}, {"Name": "ETag", "Value": "\"oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "115986"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\wrdhmnlm9b-aexeepp0ev.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.sejl45xvog.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\e55pnj8i5z-sejl45xvog.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167392032"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5973"}, {"Name": "ETag", "Value": "\"2psAeYsQwIU+4hjhaa7aiuEI8eM0VqXG4mLMI2LB5kg=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sejl45xvog"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}, {"Name": "integrity", "Value": "sha256-hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.sejl45xvog.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51800"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sejl45xvog"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}, {"Name": "integrity", "Value": "sha256-hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.sejl45xvog.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\e55pnj8i5z-sejl45xvog.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5973"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2psAeYsQwIU+4hjhaa7aiuEI8eM0VqXG4mLMI2LB5kg=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sejl45xvog"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz"}, {"Name": "integrity", "Value": "sha256-2psAeYsQwIU+4hjhaa7aiuEI8eM0VqXG4mLMI2LB5kg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ekatybltws-xvp3kq03qx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148016578"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6755"}, {"Name": "ETag", "Value": "\"Jb3hghPX8HIvLINQ2LRDLH33mFfqyzbk+nTsbiHtKr4=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74486"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.ausgxo2sd3.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\9g9fksc2vy-ausgxo2sd3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030493383"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32793"}, {"Name": "ETag", "Value": "\"cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausgxo2sd3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.ausgxo2sd3.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "203225"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausgxo2sd3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.ausgxo2sd3.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\9g9fksc2vy-ausgxo2sd3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32793"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausgxo2sd3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz"}, {"Name": "integrity", "Value": "sha256-cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ekatybltws-xvp3kq03qx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6755"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Jb3hghPX8HIvLINQ2LRDLH33mFfqyzbk+nTsbiHtKr4=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jb3hghPX8HIvLINQ2LRDLH33mFfqyzbk+nTsbiHtKr4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\9g9fksc2vy-ausgxo2sd3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030493383"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32793"}, {"Name": "ETag", "Value": "\"cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "203225"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\9g9fksc2vy-ausgxo2sd3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32793"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.22vffe00uq.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\vkm14ixwgx-22vffe00uq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167308014"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5976"}, {"Name": "ETag", "Value": "\"Gjkg0hmhA9Aga0f2ZS8ZqJ/GES4kxIvtWTfabr1bc2Y=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "22vffe00uq"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.22vffe00uq.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51875"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "22vffe00uq"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.22vffe00uq.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\vkm14ixwgx-22vffe00uq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5976"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Gjkg0hmhA9Aga0f2ZS8ZqJ/GES4kxIvtWTfabr1bc2Y=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "22vffe00uq"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz"}, {"Name": "integrity", "Value": "sha256-Gjkg0hmhA9Aga0f2ZS8ZqJ/GES4kxIvtWTfabr1bc2Y="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\vkm14ixwgx-22vffe00uq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167308014"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5976"}, {"Name": "ETag", "Value": "\"Gjkg0hmhA9Aga0f2ZS8ZqJ/GES4kxIvtWTfabr1bc2Y=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51875"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.cosvhxvwiu.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\gzxir09ed5-cosvhxvwiu.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072379849"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13815"}, {"Name": "ETag", "Value": "\"V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cosvhxvwiu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.cosvhxvwiu.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "116063"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cosvhxvwiu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.cosvhxvwiu.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\gzxir09ed5-cosvhxvwiu.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13815"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cosvhxvwiu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\vkm14ixwgx-22vffe00uq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5976"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Gjkg0hmhA9Aga0f2ZS8ZqJ/GES4kxIvtWTfabr1bc2Y=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Gjkg0hmhA9Aga0f2ZS8ZqJ/GES4kxIvtWTfabr1bc2Y="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\gzxir09ed5-cosvhxvwiu.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072379849"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13815"}, {"Name": "ETag", "Value": "\"V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "116063"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\gzxir09ed5-cosvhxvwiu.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13815"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.xvp3kq03qx.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ekatybltws-xvp3kq03qx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148016578"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6755"}, {"Name": "ETag", "Value": "\"Jb3hghPX8HIvLINQ2LRDLH33mFfqyzbk+nTsbiHtKr4=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xvp3kq03qx"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}, {"Name": "integrity", "Value": "sha256-M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.xvp3kq03qx.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74486"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xvp3kq03qx"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}, {"Name": "integrity", "Value": "sha256-M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.xvp3kq03qx.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ekatybltws-xvp3kq03qx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6755"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Jb3hghPX8HIvLINQ2LRDLH33mFfqyzbk+nTsbiHtKr4=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xvp3kq03qx"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz"}, {"Name": "integrity", "Value": "sha256-Jb3hghPX8HIvLINQ2LRDLH33mFfqyzbk+nTsbiHtKr4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.t1cqhe9u97.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\docid0nk50-t1cqhe9u97.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148038490"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6754"}, {"Name": "ETag", "Value": "\"td/WnErsJHKP3LT8Bz6znCdABuCWnP513FYSQpHRK9A=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t1cqhe9u97"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css"}, {"Name": "integrity", "Value": "sha256-f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.t1cqhe9u97.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74413"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t1cqhe9u97"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css"}, {"Name": "integrity", "Value": "sha256-f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.t1cqhe9u97.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\docid0nk50-t1cqhe9u97.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6754"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"td/WnErsJHKP3LT8Bz6znCdABuCWnP513FYSQpHRK9A=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t1cqhe9u97"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.gz"}, {"Name": "integrity", "Value": "sha256-td/WnErsJHKP3LT8Bz6znCdABuCWnP513FYSQpHRK9A="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\5gjnp28z46-qesaa3a1fm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000293427230"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3407"}, {"Name": "ETag", "Value": "\"H9IPXBzUtJGG7PJdjNTz1WD7YcFYCi+22bHkHPR+CdY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12661"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.fvhpjtyr6v.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\22yenftene-fvhpjtyr6v.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038726667"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25821"}, {"Name": "ETag", "Value": "\"FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fvhpjtyr6v"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, {"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.fvhpjtyr6v.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "129371"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fvhpjtyr6v"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, {"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.fvhpjtyr6v.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\22yenftene-fvhpjtyr6v.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25821"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fvhpjtyr6v"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz"}, {"Name": "integrity", "Value": "sha256-FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\5gjnp28z46-qesaa3a1fm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3407"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"H9IPXBzUtJGG7PJdjNTz1WD7YcFYCi+22bHkHPR+CdY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H9IPXBzUtJGG7PJdjNTz1WD7YcFYCi+22bHkHPR+CdY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\22yenftene-fvhpjtyr6v.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038726667"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25821"}, {"Name": "ETag", "Value": "\"FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "129371"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\22yenftene-fvhpjtyr6v.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25821"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\vz1dxm0tda-tmc1g35s3z.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000310752020"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3217"}, {"Name": "ETag", "Value": "\"w8PM3Ms+TEJVQ/PGIsRL/kn0h83Pm5ek8Sm7/zkgCYc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10131"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.fsbi9cje9m.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ys21i01zgw-fsbi9cje9m.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000079440737"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12587"}, {"Name": "ETag", "Value": "\"crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fsbi9cje9m"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, {"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.fsbi9cje9m.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51369"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fsbi9cje9m"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, {"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.fsbi9cje9m.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ys21i01zgw-fsbi9cje9m.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12587"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fsbi9cje9m"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\vz1dxm0tda-tmc1g35s3z.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3217"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"w8PM3Ms+TEJVQ/PGIsRL/kn0h83Pm5ek8Sm7/zkgCYc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-w8PM3Ms+TEJVQ/PGIsRL/kn0h83Pm5ek8Sm7/zkgCYc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ys21i01zgw-fsbi9cje9m.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000079440737"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12587"}, {"Name": "ETag", "Value": "\"crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51369"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ys21i01zgw-fsbi9cje9m.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12587"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.tmc1g35s3z.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\vz1dxm0tda-tmc1g35s3z.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000310752020"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3217"}, {"Name": "ETag", "Value": "\"w8PM3Ms+TEJVQ/PGIsRL/kn0h83Pm5ek8Sm7/zkgCYc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tmc1g35s3z"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, {"Name": "integrity", "Value": "sha256-y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.tmc1g35s3z.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10131"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tmc1g35s3z"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, {"Name": "integrity", "Value": "sha256-y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.tmc1g35s3z.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\vz1dxm0tda-tmc1g35s3z.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3217"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"w8PM3Ms+TEJVQ/PGIsRL/kn0h83Pm5ek8Sm7/zkgCYc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tmc1g35s3z"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz"}, {"Name": "integrity", "Value": "sha256-w8PM3Ms+TEJVQ/PGIsRL/kn0h83Pm5ek8Sm7/zkgCYc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.qesaa3a1fm.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\5gjnp28z46-qesaa3a1fm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000293427230"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3407"}, {"Name": "ETag", "Value": "\"H9IPXBzUtJGG7PJdjNTz1WD7YcFYCi+22bHkHPR+CdY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qesaa3a1fm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css"}, {"Name": "integrity", "Value": "sha256-rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.qesaa3a1fm.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12661"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qesaa3a1fm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css"}, {"Name": "integrity", "Value": "sha256-rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.qesaa3a1fm.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\5gjnp28z46-qesaa3a1fm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3407"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"H9IPXBzUtJGG7PJdjNTz1WD7YcFYCi+22bHkHPR+CdY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qesaa3a1fm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz"}, {"Name": "integrity", "Value": "sha256-H9IPXBzUtJGG7PJdjNTz1WD7YcFYCi+22bHkHPR+CdY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\8a0aj51wdw-rxsg74s51o.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000294290759"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3397"}, {"Name": "ETag", "Value": "\"ueerZxLdSKIEsWJ13wzBfTcRfd3uGSfOQwhUfM1jRec=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12651"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.ee0r1s7dh0.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\2cc7g8dnkc-ee0r1s7dh0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038708678"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25833"}, {"Name": "ETag", "Value": "\"wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ee0r1s7dh0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.ee0r1s7dh0.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "129386"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ee0r1s7dh0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.ee0r1s7dh0.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\2cc7g8dnkc-ee0r1s7dh0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25833"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ee0r1s7dh0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz"}, {"Name": "integrity", "Value": "sha256-wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\8a0aj51wdw-rxsg74s51o.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3397"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ueerZxLdSKIEsWJ13wzBfTcRfd3uGSfOQwhUfM1jRec=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ueerZxLdSKIEsWJ13wzBfTcRfd3uGSfOQwhUfM1jRec="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\2cc7g8dnkc-ee0r1s7dh0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038708678"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25833"}, {"Name": "ETag", "Value": "\"wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "129386"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\2cc7g8dnkc-ee0r1s7dh0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25833"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\fcr8eb906x-q9ht133ko3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000307597662"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3250"}, {"Name": "ETag", "Value": "\"AX9UjSDaqolhmzTa/KUDWMjLF2XlYT6I6JZuUGrm1nw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10203"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\fcr8eb906x-q9ht133ko3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3250"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AX9UjSDaqolhmzTa/KUDWMjLF2XlYT6I6JZuUGrm1nw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AX9UjSDaqolhmzTa/KUDWMjLF2XlYT6I6JZuUGrm1nw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.jd9uben2k1.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\krrntovrdp-jd9uben2k1.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000066423115"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15054"}, {"Name": "ETag", "Value": "\"V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jd9uben2k1"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.jd9uben2k1.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "63943"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jd9uben2k1"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.jd9uben2k1.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\krrntovrdp-jd9uben2k1.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15054"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jd9uben2k1"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\krrntovrdp-jd9uben2k1.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000066423115"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15054"}, {"Name": "ETag", "Value": "\"V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "63943"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\krrntovrdp-jd9uben2k1.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15054"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.q9ht133ko3.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\fcr8eb906x-q9ht133ko3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000307597662"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3250"}, {"Name": "ETag", "Value": "\"AX9UjSDaqolhmzTa/KUDWMjLF2XlYT6I6JZuUGrm1nw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q9ht133ko3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.q9ht133ko3.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10203"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q9ht133ko3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.q9ht133ko3.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\fcr8eb906x-q9ht133ko3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3250"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AX9UjSDaqolhmzTa/KUDWMjLF2XlYT6I6JZuUGrm1nw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q9ht133ko3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz"}, {"Name": "integrity", "Value": "sha256-AX9UjSDaqolhmzTa/KUDWMjLF2XlYT6I6JZuUGrm1nw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.rxsg74s51o.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\8a0aj51wdw-rxsg74s51o.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000294290759"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3397"}, {"Name": "ETag", "Value": "\"ueerZxLdSKIEsWJ13wzBfTcRfd3uGSfOQwhUfM1jRec=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rxsg74s51o"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}, {"Name": "integrity", "Value": "sha256-gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.rxsg74s51o.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12651"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rxsg74s51o"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}, {"Name": "integrity", "Value": "sha256-gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.rxsg74s51o.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\8a0aj51wdw-rxsg74s51o.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3397"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ueerZxLdSKIEsWJ13wzBfTcRfd3uGSfOQwhUfM1jRec=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rxsg74s51o"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz"}, {"Name": "integrity", "Value": "sha256-ueerZxLdSKIEsWJ13wzBfTcRfd3uGSfOQwhUfM1jRec="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\nuxbc26o8n-gye83jo8yx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000082767754"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12081"}, {"Name": "ETag", "Value": "\"VGKPtySribew+qZrTcy+fpnTi7Q3gxSv+HWQQ/4rQos=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "113224"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\nuxbc26o8n-gye83jo8yx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12081"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VGKPtySribew+qZrTcy+fpnTi7Q3gxSv+HWQQ/4rQos=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VGKPtySribew+qZrTcy+fpnTi7Q3gxSv+HWQQ/4rQos="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\v6x955lyjj-r4e9w2rdcm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022663403"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44123"}, {"Name": "ETag", "Value": "\"kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\v6x955lyjj-r4e9w2rdcm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.r4e9w2rdcm.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\v6x955lyjj-r4e9w2rdcm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022663403"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44123"}, {"Name": "ETag", "Value": "\"kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4e9w2rdcm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map"}, {"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.r4e9w2rdcm.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4e9w2rdcm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map"}, {"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.r4e9w2rdcm.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\v6x955lyjj-r4e9w2rdcm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4e9w2rdcm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz"}, {"Name": "integrity", "Value": "sha256-kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.gye83jo8yx.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\nuxbc26o8n-gye83jo8yx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000082767754"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12081"}, {"Name": "ETag", "Value": "\"VGKPtySribew+qZrTcy+fpnTi7Q3gxSv+HWQQ/4rQos=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gye83jo8yx"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css"}, {"Name": "integrity", "Value": "sha256-uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.gye83jo8yx.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "113224"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gye83jo8yx"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css"}, {"Name": "integrity", "Value": "sha256-uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.gye83jo8yx.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\nuxbc26o8n-gye83jo8yx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12081"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VGKPtySribew+qZrTcy+fpnTi7Q3gxSv+HWQQ/4rQos=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gye83jo8yx"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz"}, {"Name": "integrity", "Value": "sha256-VGKPtySribew+qZrTcy+fpnTi7Q3gxSv+HWQQ/4rQos="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ovchpumm0y-wl58j5mj3v.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090350560"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11067"}, {"Name": "ETag", "Value": "\"cxoqFtEDkm4KCYDrmWgnE7zkiXwSKUlq/rctM76ouDg=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85357"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.c2oey78nd0.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\1ork37jani-c2oey78nd0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041081259"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24341"}, {"Name": "ETag", "Value": "\"Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2oey78nd0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}, {"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.c2oey78nd0.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180381"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2oey78nd0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}, {"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.c2oey78nd0.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\1ork37jani-c2oey78nd0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24341"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2oey78nd0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ovchpumm0y-wl58j5mj3v.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11067"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"cxoqFtEDkm4KCYDrmWgnE7zkiXwSKUlq/rctM76ouDg=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cxoqFtEDkm4KCYDrmWgnE7zkiXwSKUlq/rctM76ouDg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\1ork37jani-c2oey78nd0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041081259"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24341"}, {"Name": "ETag", "Value": "\"Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180381"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\1ork37jani-c2oey78nd0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24341"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.wl58j5mj3v.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ovchpumm0y-wl58j5mj3v.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090350560"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11067"}, {"Name": "ETag", "Value": "\"cxoqFtEDkm4KCYDrmWgnE7zkiXwSKUlq/rctM76ouDg=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wl58j5mj3v"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css"}, {"Name": "integrity", "Value": "sha256-ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.wl58j5mj3v.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85357"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wl58j5mj3v"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css"}, {"Name": "integrity", "Value": "sha256-ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.wl58j5mj3v.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ovchpumm0y-wl58j5mj3v.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11067"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"cxoqFtEDkm4KCYDrmWgnE7zkiXwSKUlq/rctM76ouDg=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wl58j5mj3v"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz"}, {"Name": "integrity", "Value": "sha256-cxoqFtEDkm4KCYDrmWgnE7zkiXwSKUlq/rctM76ouDg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\pybm8mc09z-d4r6k3f320.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083097889"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12033"}, {"Name": "ETag", "Value": "\"rLnVjI3ToFgRadgC+qnQqkjBbeGzyUk/W1M+MRurfiM=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "113083"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\pybm8mc09z-d4r6k3f320.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12033"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rLnVjI3ToFgRadgC+qnQqkjBbeGzyUk/W1M+MRurfiM=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rLnVjI3ToFgRadgC+qnQqkjBbeGzyUk/W1M+MRurfiM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.j5mq2jizvt.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\3uw1dz2yv7-j5mq2jizvt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022677794"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44095"}, {"Name": "ETag", "Value": "\"KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5mq2jizvt"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.j5mq2jizvt.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267476"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5mq2jizvt"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.j5mq2jizvt.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\3uw1dz2yv7-j5mq2jizvt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44095"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5mq2jizvt"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz"}, {"Name": "integrity", "Value": "sha256-KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\3uw1dz2yv7-j5mq2jizvt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022677794"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44095"}, {"Name": "ETag", "Value": "\"KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267476"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\3uw1dz2yv7-j5mq2jizvt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44095"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.d4r6k3f320.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\pybm8mc09z-d4r6k3f320.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083097889"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12033"}, {"Name": "ETag", "Value": "\"rLnVjI3ToFgRadgC+qnQqkjBbeGzyUk/W1M+MRurfiM=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d4r6k3f320"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}, {"Name": "integrity", "Value": "sha256-MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.d4r6k3f320.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "113083"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d4r6k3f320"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}, {"Name": "integrity", "Value": "sha256-MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.d4r6k3f320.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\pybm8mc09z-d4r6k3f320.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12033"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rLnVjI3ToFgRadgC+qnQqkjBbeGzyUk/W1M+MRurfiM=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d4r6k3f320"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz"}, {"Name": "integrity", "Value": "sha256-rLnVjI3ToFgRadgC+qnQqkjBbeGzyUk/W1M+MRurfiM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\bkl21n1ixh-keugtjm085.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090489548"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11050"}, {"Name": "ETag", "Value": "\"Fi8DMQpQ0pHVk8uDPzXmz7v1U9ElGm/J/lTUl54WMHw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85286"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\bkl21n1ixh-keugtjm085.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11050"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Fi8DMQpQ0pHVk8uDPzXmz7v1U9ElGm/J/lTUl54WMHw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Fi8DMQpQ0pHVk8uDPzXmz7v1U9ElGm/J/lTUl54WMHw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\jsy329rgjt-nvvlpmu67g.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041162427"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24293"}, {"Name": "ETag", "Value": "\"8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180217"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\jsy329rgjt-nvvlpmu67g.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24293"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.nvvlpmu67g.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\jsy329rgjt-nvvlpmu67g.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041162427"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24293"}, {"Name": "ETag", "Value": "\"8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nvvlpmu67g"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.nvvlpmu67g.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180217"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nvvlpmu67g"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.nvvlpmu67g.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\jsy329rgjt-nvvlpmu67g.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24293"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nvvlpmu67g"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.keugtjm085.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\bkl21n1ixh-keugtjm085.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090489548"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11050"}, {"Name": "ETag", "Value": "\"Fi8DMQpQ0pHVk8uDPzXmz7v1U9ElGm/J/lTUl54WMHw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "keugtjm085"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.keugtjm085.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85286"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "keugtjm085"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.keugtjm085.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\bkl21n1ixh-keugtjm085.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11050"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Fi8DMQpQ0pHVk8uDPzXmz7v1U9ElGm/J/lTUl54WMHw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "keugtjm085"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz"}, {"Name": "integrity", "Value": "sha256-Fi8DMQpQ0pHVk8uDPzXmz7v1U9ElGm/J/lTUl54WMHw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\c6qmdw4ri0-zub09dkrxp.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000029833826"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33518"}, {"Name": "ETag", "Value": "\"j8xOcokVqVMlkhtMkAD+dCGjp8eCK+RXUPREaOqe2TU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "293102"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\c6qmdw4ri0-zub09dkrxp.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33518"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j8xOcokVqVMlkhtMkAD+dCGjp8eCK+RXUPREaOqe2TU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j8xOcokVqVMlkhtMkAD+dCGjp8eCK+RXUPREaOqe2TU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ktuithl01y-pj5nd1wqec.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008694896"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "115009"}, {"Name": "ETag", "Value": "\"M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "679755"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ktuithl01y-pj5nd1wqec.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "115009"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.pj5nd1wqec.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ktuithl01y-pj5nd1wqec.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008694896"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "115009"}, {"Name": "ETag", "Value": "\"M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pj5nd1wqec"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map"}, {"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.pj5nd1wqec.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "679755"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pj5nd1wqec"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map"}, {"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.pj5nd1wqec.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ktuithl01y-pj5nd1wqec.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "115009"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pj5nd1wqec"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map.gz"}, {"Name": "integrity", "Value": "sha256-M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.43atpzeawx.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\xv7sgvmssy-43atpzeawx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032290355"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30968"}, {"Name": "ETag", "Value": "\"ZA1L2PX1u6VKIuxhJUgeKELAZwIreHb7fDdqlTEZyKs=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "43atpzeawx"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.43atpzeawx.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232808"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "43atpzeawx"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.43atpzeawx.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\xv7sgvmssy-43atpzeawx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30968"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZA1L2PX1u6VKIuxhJUgeKELAZwIreHb7fDdqlTEZyKs=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "43atpzeawx"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.gz"}, {"Name": "integrity", "Value": "sha256-ZA1L2PX1u6VKIuxhJUgeKELAZwIreHb7fDdqlTEZyKs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\xv7sgvmssy-43atpzeawx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032290355"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30968"}, {"Name": "ETag", "Value": "\"ZA1L2PX1u6VKIuxhJUgeKELAZwIreHb7fDdqlTEZyKs=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232808"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\xv7sgvmssy-43atpzeawx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30968"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZA1L2PX1u6VKIuxhJUgeKELAZwIreHb7fDdqlTEZyKs=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZA1L2PX1u6VKIuxhJUgeKELAZwIreHb7fDdqlTEZyKs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\6e4f3aw9u8-v0zj4ognzu.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010892297"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91807"}, {"Name": "ETag", "Value": "\"+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "589892"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\6e4f3aw9u8-v0zj4ognzu.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.v0zj4ognzu.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\6e4f3aw9u8-v0zj4ognzu.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010892297"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91807"}, {"Name": "ETag", "Value": "\"+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v0zj4ognzu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.v0zj4ognzu.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "589892"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v0zj4ognzu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.v0zj4ognzu.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\6e4f3aw9u8-v0zj4ognzu.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v0zj4ognzu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\c1xriqn00w-ynyaa8k90p.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000029962547"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33374"}, {"Name": "ETag", "Value": "\"CKd1DZe0tmjYGXJCdpcyExOT7GvFEZKGtk3+Ij7ZTSc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "292288"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\c1xriqn00w-ynyaa8k90p.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33374"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CKd1DZe0tmjYGXJCdpcyExOT7GvFEZKGtk3+Ij7ZTSc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CKd1DZe0tmjYGXJCdpcyExOT7GvFEZKGtk3+Ij7ZTSc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.hrwsygsryq.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\iyscw6kjtk-hrwsygsryq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008699132"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114953"}, {"Name": "ETag", "Value": "\"xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrwsygsryq"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.hrwsygsryq.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "679615"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrwsygsryq"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.hrwsygsryq.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\iyscw6kjtk-hrwsygsryq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114953"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrwsygsryq"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz"}, {"Name": "integrity", "Value": "sha256-xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\iyscw6kjtk-hrwsygsryq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008699132"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114953"}, {"Name": "ETag", "Value": "\"xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "679615"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\iyscw6kjtk-hrwsygsryq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114953"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.c63t5i9ira.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\11y2nf4j36-c63t5i9ira.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032266391"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30991"}, {"Name": "ETag", "Value": "\"zxq4fMIq5J99E6EK1H7qVe10DUZ2GcXauY+j6URB7DI=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c63t5i9ira"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.c63t5i9ira.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232916"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c63t5i9ira"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.c63t5i9ira.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\11y2nf4j36-c63t5i9ira.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30991"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zxq4fMIq5J99E6EK1H7qVe10DUZ2GcXauY+j6URB7DI=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c63t5i9ira"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz"}, {"Name": "integrity", "Value": "sha256-zxq4fMIq5J99E6EK1H7qVe10DUZ2GcXauY+j6URB7DI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\11y2nf4j36-c63t5i9ira.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032266391"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30991"}, {"Name": "ETag", "Value": "\"zxq4fMIq5J99E6EK1H7qVe10DUZ2GcXauY+j6URB7DI=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232916"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.ft3s53vfgj.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\zt8myftf3o-ft3s53vfgj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010904769"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91702"}, {"Name": "ETag", "Value": "\"mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ft3s53vfgj"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.ft3s53vfgj.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "589087"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ft3s53vfgj"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.ft3s53vfgj.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\zt8myftf3o-ft3s53vfgj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91702"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ft3s53vfgj"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\11y2nf4j36-c63t5i9ira.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30991"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zxq4fMIq5J99E6EK1H7qVe10DUZ2GcXauY+j6URB7DI=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zxq4fMIq5J99E6EK1H7qVe10DUZ2GcXauY+j6URB7DI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\zt8myftf3o-ft3s53vfgj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010904769"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91702"}, {"Name": "ETag", "Value": "\"mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "589087"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\zt8myftf3o-ft3s53vfgj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91702"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.ynyaa8k90p.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\c1xriqn00w-ynyaa8k90p.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000029962547"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33374"}, {"Name": "ETag", "Value": "\"CKd1DZe0tmjYGXJCdpcyExOT7GvFEZKGtk3+Ij7ZTSc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ynyaa8k90p"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css"}, {"Name": "integrity", "Value": "sha256-/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.ynyaa8k90p.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "292288"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ynyaa8k90p"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css"}, {"Name": "integrity", "Value": "sha256-/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.ynyaa8k90p.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\c1xriqn00w-ynyaa8k90p.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33374"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CKd1DZe0tmjYGXJCdpcyExOT7GvFEZKGtk3+Ij7ZTSc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ynyaa8k90p"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz"}, {"Name": "integrity", "Value": "sha256-CKd1DZe0tmjYGXJCdpcyExOT7GvFEZKGtk3+Ij7ZTSc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.zub09dkrxp.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\c6qmdw4ri0-zub09dkrxp.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000029833826"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33518"}, {"Name": "ETag", "Value": "\"j8xOcokVqVMlkhtMkAD+dCGjp8eCK+RXUPREaOqe2TU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zub09dkrxp"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css"}, {"Name": "integrity", "Value": "sha256-CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.zub09dkrxp.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "293102"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zub09dkrxp"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css"}, {"Name": "integrity", "Value": "sha256-CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.zub09dkrxp.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\c6qmdw4ri0-zub09dkrxp.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33518"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j8xOcokVqVMlkhtMkAD+dCGjp8eCK+RXUPREaOqe2TU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zub09dkrxp"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.gz"}, {"Name": "integrity", "Value": "sha256-j8xOcokVqVMlkhtMkAD+dCGjp8eCK+RXUPREaOqe2TU="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.1jo1kezdci.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\oac7z3pbsy-1jo1kezdci.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000025732740"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "38860"}, {"Name": "ETag", "Value": "\"G3hEVXOXq2YI9Z7/yvAQvvf6j7gWnCvdHiIIKSi826M=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"k9Hcnfw1uBvIz3LBQJE2enuHAQ+2vcGQx7FY/sjMCT0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1jo1kezdci"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js"}, {"Name": "integrity", "Value": "sha256-k9Hcnfw1uBvIz3LBQJE2enuHAQ+2vcGQx7FY/sjMCT0="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.1jo1kezdci.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "167465"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"k9Hcnfw1uBvIz3LBQJE2enuHAQ+2vcGQx7FY/sjMCT0=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1jo1kezdci"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js"}, {"Name": "integrity", "Value": "sha256-k9Hcnfw1uBvIz3LBQJE2enuHAQ+2vcGQx7FY/sjMCT0="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.1jo1kezdci.js.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\oac7z3pbsy-1jo1kezdci.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "38860"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"G3hEVXOXq2YI9Z7/yvAQvvf6j7gWnCvdHiIIKSi826M=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1jo1kezdci"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.gz"}, {"Name": "integrity", "Value": "sha256-G3hEVXOXq2YI9Z7/yvAQvvf6j7gWnCvdHiIIKSi826M="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.aak5eirmym.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\1ctw0o17fe-aak5eirmym.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000018396895"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54356"}, {"Name": "ETag", "Value": "\"8xIvF19Nlu+E+UlJdrkdtyfj4s3Cs5JfKiPF/L5QK0c=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"DoRbpT+MFNf8Kw514KCM7HP0Xkm7mSIlvI2H77dfRDA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aak5eirmym"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js"}, {"Name": "integrity", "Value": "sha256-DoRbpT+MFNf8Kw514KCM7HP0Xkm7mSIlvI2H77dfRDA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.aak5eirmym.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "231707"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"DoRbpT+MFNf8Kw514KCM7HP0Xkm7mSIlvI2H77dfRDA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aak5eirmym"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js"}, {"Name": "integrity", "Value": "sha256-DoRbpT+MFNf8Kw514KCM7HP0Xkm7mSIlvI2H77dfRDA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.aak5eirmym.js.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\1ctw0o17fe-aak5eirmym.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54356"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8xIvF19Nlu+E+UlJdrkdtyfj4s3Cs5JfKiPF/L5QK0c=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aak5eirmym"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz"}, {"Name": "integrity", "Value": "sha256-8xIvF19Nlu+E+UlJdrkdtyfj4s3Cs5JfKiPF/L5QK0c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\1ctw0o17fe-aak5eirmym.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000018396895"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54356"}, {"Name": "ETag", "Value": "\"8xIvF19Nlu+E+UlJdrkdtyfj4s3Cs5JfKiPF/L5QK0c=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"DoRbpT+MFNf8Kw514KCM7HP0Xkm7mSIlvI2H77dfRDA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DoRbpT+MFNf8Kw514KCM7HP0Xkm7mSIlvI2H77dfRDA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "231707"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"DoRbpT+MFNf8Kw514KCM7HP0Xkm7mSIlvI2H77dfRDA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DoRbpT+MFNf8Kw514KCM7HP0Xkm7mSIlvI2H77dfRDA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\1ctw0o17fe-aak5eirmym.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54356"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8xIvF19Nlu+E+UlJdrkdtyfj4s3Cs5JfKiPF/L5QK0c=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8xIvF19Nlu+E+UlJdrkdtyfj4s3Cs5JfKiPF/L5QK0c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.m8m04ow07k.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\nr8pparx2w-m8m04ow07k.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010863071"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "92054"}, {"Name": "ETag", "Value": "\"PqV7FoSrUvHZIAI97OB5Du66Rix7At2iTuksxeoo5Gk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"llRPAjsTCQdX9I6jAgtmhr1rUT9tvAVdaPQrYD38Td4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m8m04ow07k"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, {"Name": "integrity", "Value": "sha256-llRPAjsTCQdX9I6jAgtmhr1rUT9tvAVdaPQrYD38Td4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.m8m04ow07k.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "444579"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"llRPAjsTCQdX9I6jAgtmhr1rUT9tvAVdaPQrYD38Td4=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m8m04ow07k"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, {"Name": "integrity", "Value": "sha256-llRPAjsTCQdX9I6jAgtmhr1rUT9tvAVdaPQrYD38Td4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.m8m04ow07k.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\nr8pparx2w-m8m04ow07k.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "92054"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"PqV7FoSrUvHZIAI97OB5Du66Rix7At2iTuksxeoo5Gk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m8m04ow07k"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz"}, {"Name": "integrity", "Value": "sha256-PqV7FoSrUvHZIAI97OB5Du66Rix7At2iTuksxeoo5Gk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\nr8pparx2w-m8m04ow07k.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010863071"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "92054"}, {"Name": "ETag", "Value": "\"PqV7FoSrUvHZIAI97OB5Du66Rix7At2iTuksxeoo5Gk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"llRPAjsTCQdX9I6jAgtmhr1rUT9tvAVdaPQrYD38Td4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-llRPAjsTCQdX9I6jAgtmhr1rUT9tvAVdaPQrYD38Td4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "444579"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"llRPAjsTCQdX9I6jAgtmhr1rUT9tvAVdaPQrYD38Td4=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-llRPAjsTCQdX9I6jAgtmhr1rUT9tvAVdaPQrYD38Td4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\nr8pparx2w-m8m04ow07k.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "92054"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"PqV7FoSrUvHZIAI97OB5Du66Rix7At2iTuksxeoo5Gk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PqV7FoSrUvHZIAI97OB5Du66Rix7At2iTuksxeoo5Gk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\cq5p1v8hv9-sebe1i1n8x.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030426581"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32865"}, {"Name": "ETag", "Value": "\"N4UpPPH7dwqNn708OBuX/liy3v1uuPjjyR9ELovuEYw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"RCqG8kOYgTkEN6lTuil29qaURdLpMdgr2x1tKpEn2Ow=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RCqG8kOYgTkEN6lTuil29qaURdLpMdgr2x1tKpEn2Ow="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "98317"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RCqG8kOYgTkEN6lTuil29qaURdLpMdgr2x1tKpEn2Ow=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RCqG8kOYgTkEN6lTuil29qaURdLpMdgr2x1tKpEn2Ow="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.07r8e49zdp.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\vq2fdwhkxj-07r8e49zdp.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011499540"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86959"}, {"Name": "ETag", "Value": "\"S45O8CWj18jAuvMjr7tniBMZLZx6s1gfCjeu1uXTsjQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"rOniCgnoNJIVZp7PCJnzSGta8mgTGweOpxp/z4+Oc+k=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "07r8e49zdp"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, {"Name": "integrity", "Value": "sha256-rOniCgnoNJIVZp7PCJnzSGta8mgTGweOpxp/z4+Oc+k="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.07r8e49zdp.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "332090"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rOniCgnoNJIVZp7PCJnzSGta8mgTGweOpxp/z4+Oc+k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "07r8e49zdp"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, {"Name": "integrity", "Value": "sha256-rOniCgnoNJIVZp7PCJnzSGta8mgTGweOpxp/z4+Oc+k="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.07r8e49zdp.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\vq2fdwhkxj-07r8e49zdp.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86959"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"S45O8CWj18jAuvMjr7tniBMZLZx6s1gfCjeu1uXTsjQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "07r8e49zdp"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz"}, {"Name": "integrity", "Value": "sha256-S45O8CWj18jAuvMjr7tniBMZLZx6s1gfCjeu1uXTsjQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\cq5p1v8hv9-sebe1i1n8x.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32865"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N4UpPPH7dwqNn708OBuX/liy3v1uuPjjyR9ELovuEYw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-N4UpPPH7dwqNn708OBuX/liy3v1uuPjjyR9ELovuEYw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\vq2fdwhkxj-07r8e49zdp.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011499540"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86959"}, {"Name": "ETag", "Value": "\"S45O8CWj18jAuvMjr7tniBMZLZx6s1gfCjeu1uXTsjQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"rOniCgnoNJIVZp7PCJnzSGta8mgTGweOpxp/z4+Oc+k=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rOniCgnoNJIVZp7PCJnzSGta8mgTGweOpxp/z4+Oc+k="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "332090"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rOniCgnoNJIVZp7PCJnzSGta8mgTGweOpxp/z4+Oc+k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rOniCgnoNJIVZp7PCJnzSGta8mgTGweOpxp/z4+Oc+k="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\vq2fdwhkxj-07r8e49zdp.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86959"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"S45O8CWj18jAuvMjr7tniBMZLZx6s1gfCjeu1uXTsjQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S45O8CWj18jAuvMjr7tniBMZLZx6s1gfCjeu1uXTsjQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.sebe1i1n8x.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\cq5p1v8hv9-sebe1i1n8x.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030426581"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32865"}, {"Name": "ETag", "Value": "\"N4UpPPH7dwqNn708OBuX/liy3v1uuPjjyR9ELovuEYw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"RCqG8kOYgTkEN6lTuil29qaURdLpMdgr2x1tKpEn2Ow=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sebe1i1n8x"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, {"Name": "integrity", "Value": "sha256-RCqG8kOYgTkEN6lTuil29qaURdLpMdgr2x1tKpEn2Ow="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.sebe1i1n8x.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "98317"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RCqG8kOYgTkEN6lTuil29qaURdLpMdgr2x1tKpEn2Ow=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sebe1i1n8x"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, {"Name": "integrity", "Value": "sha256-RCqG8kOYgTkEN6lTuil29qaURdLpMdgr2x1tKpEn2Ow="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.sebe1i1n8x.js.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\cq5p1v8hv9-sebe1i1n8x.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32865"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N4UpPPH7dwqNn708OBuX/liy3v1uuPjjyR9ELovuEYw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sebe1i1n8x"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz"}, {"Name": "integrity", "Value": "sha256-N4UpPPH7dwqNn708OBuX/liy3v1uuPjjyR9ELovuEYw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\zgyeaunyzm-xy033osspr.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000026304022"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "38016"}, {"Name": "ETag", "Value": "\"kIioyvmf/tLdemeqRPWlzFgcfoZ1Ove/FdckmYDBXpk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"V9VHrfWTQdDZhpDjgAzkkk7JIEn/cx3lMtjiDUXul/Q=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V9VHrfWTQdDZhpDjgAzkkk7JIEn/cx3lMtjiDUXul/Q="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "157850"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"V9VHrfWTQdDZhpDjgAzkkk7JIEn/cx3lMtjiDUXul/Q=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V9VHrfWTQdDZhpDjgAzkkk7JIEn/cx3lMtjiDUXul/Q="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\zgyeaunyzm-xy033osspr.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "38016"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kIioyvmf/tLdemeqRPWlzFgcfoZ1Ove/FdckmYDBXpk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kIioyvmf/tLdemeqRPWlzFgcfoZ1Ove/FdckmYDBXpk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\w8qngq9ie1-sybl3t77fa.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015593812"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64127"}, {"Name": "ETag", "Value": "\"kXW7yFoYyoJuZ8DvEVLMROrQ/SMEw2Pbr7LLiGIZlCY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"1NZ0e0qqYzJigO5XFb9Y2bgUwRxi1f/jpwe4eU6A5pA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1NZ0e0qqYzJigO5XFb9Y2bgUwRxi1f/jpwe4eU6A5pA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "305438"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"1NZ0e0qqYzJigO5XFb9Y2bgUwRxi1f/jpwe4eU6A5pA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1NZ0e0qqYzJigO5XFb9Y2bgUwRxi1f/jpwe4eU6A5pA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\w8qngq9ie1-sybl3t77fa.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64127"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kXW7yFoYyoJuZ8DvEVLMROrQ/SMEw2Pbr7LLiGIZlCY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kXW7yFoYyoJuZ8DvEVLMROrQ/SMEw2Pbr7LLiGIZlCY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.sybl3t77fa.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\w8qngq9ie1-sybl3t77fa.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015593812"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64127"}, {"Name": "ETag", "Value": "\"kXW7yFoYyoJuZ8DvEVLMROrQ/SMEw2Pbr7LLiGIZlCY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"1NZ0e0qqYzJigO5XFb9Y2bgUwRxi1f/jpwe4eU6A5pA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sybl3t77fa"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map"}, {"Name": "integrity", "Value": "sha256-1NZ0e0qqYzJigO5XFb9Y2bgUwRxi1f/jpwe4eU6A5pA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.sybl3t77fa.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "305438"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"1NZ0e0qqYzJigO5XFb9Y2bgUwRxi1f/jpwe4eU6A5pA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sybl3t77fa"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map"}, {"Name": "integrity", "Value": "sha256-1NZ0e0qqYzJigO5XFb9Y2bgUwRxi1f/jpwe4eU6A5pA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.sybl3t77fa.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\w8qngq9ie1-sybl3t77fa.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64127"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kXW7yFoYyoJuZ8DvEVLMROrQ/SMEw2Pbr7LLiGIZlCY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sybl3t77fa"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz"}, {"Name": "integrity", "Value": "sha256-kXW7yFoYyoJuZ8DvEVLMROrQ/SMEw2Pbr7LLiGIZlCY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\qbt2gjo1ua-l53j0jwsua.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000035487420"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28178"}, {"Name": "ETag", "Value": "\"xhw67ZJiHnM6w7VhJZ0xa4j4CdRyJh0f/YiG0s0T63k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"Rz2BsZTYP8L0/pkMgZIAMFzaFfsMWc+8175AqV69XCE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Rz2BsZTYP8L0/pkMgZIAMFzaFfsMWc+8175AqV69XCE="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91499"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Rz2BsZTYP8L0/pkMgZIAMFzaFfsMWc+8175AqV69XCE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Rz2BsZTYP8L0/pkMgZIAMFzaFfsMWc+8175AqV69XCE="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\qbt2gjo1ua-l53j0jwsua.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28178"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xhw67ZJiHnM6w7VhJZ0xa4j4CdRyJh0f/YiG0s0T63k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xhw67ZJiHnM6w7VhJZ0xa4j4CdRyJh0f/YiG0s0T63k="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\sbc2oexvxn-u3orf5n9fb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017645398"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56671"}, {"Name": "ETag", "Value": "\"gOJ+qyInHxVGQAHaRs9ggiXfugMTsR/8thKNQLja68A=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"ygX//9b5IsEzbh1wXO6jfbN5R0YVHtui5Zm2Pxzfows=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ygX//9b5IsEzbh1wXO6jfbN5R0YVHtui5Zm2Pxzfows="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "222455"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ygX//9b5IsEzbh1wXO6jfbN5R0YVHtui5Zm2Pxzfows=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ygX//9b5IsEzbh1wXO6jfbN5R0YVHtui5Zm2Pxzfows="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\sbc2oexvxn-u3orf5n9fb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56671"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gOJ+qyInHxVGQAHaRs9ggiXfugMTsR/8thKNQLja68A=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gOJ+qyInHxVGQAHaRs9ggiXfugMTsR/8thKNQLja68A="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.u3orf5n9fb.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\sbc2oexvxn-u3orf5n9fb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017645398"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56671"}, {"Name": "ETag", "Value": "\"gOJ+qyInHxVGQAHaRs9ggiXfugMTsR/8thKNQLja68A=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"ygX//9b5IsEzbh1wXO6jfbN5R0YVHtui5Zm2Pxzfows=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u3orf5n9fb"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}, {"Name": "integrity", "Value": "sha256-ygX//9b5IsEzbh1wXO6jfbN5R0YVHtui5Zm2Pxzfows="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.u3orf5n9fb.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "222455"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ygX//9b5IsEzbh1wXO6jfbN5R0YVHtui5Zm2Pxzfows=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u3orf5n9fb"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}, {"Name": "integrity", "Value": "sha256-ygX//9b5IsEzbh1wXO6jfbN5R0YVHtui5Zm2Pxzfows="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.u3orf5n9fb.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\sbc2oexvxn-u3orf5n9fb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56671"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gOJ+qyInHxVGQAHaRs9ggiXfugMTsR/8thKNQLja68A=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u3orf5n9fb"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz"}, {"Name": "integrity", "Value": "sha256-gOJ+qyInHxVGQAHaRs9ggiXfugMTsR/8thKNQLja68A="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.l53j0jwsua.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\qbt2gjo1ua-l53j0jwsua.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000035487420"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28178"}, {"Name": "ETag", "Value": "\"xhw67ZJiHnM6w7VhJZ0xa4j4CdRyJh0f/YiG0s0T63k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"Rz2BsZTYP8L0/pkMgZIAMFzaFfsMWc+8175AqV69XCE=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "l53j0jwsua"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js"}, {"Name": "integrity", "Value": "sha256-Rz2BsZTYP8L0/pkMgZIAMFzaFfsMWc+8175AqV69XCE="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.l53j0jwsua.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91499"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Rz2BsZTYP8L0/pkMgZIAMFzaFfsMWc+8175AqV69XCE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "l53j0jwsua"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js"}, {"Name": "integrity", "Value": "sha256-Rz2BsZTYP8L0/pkMgZIAMFzaFfsMWc+8175AqV69XCE="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.l53j0jwsua.js.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\qbt2gjo1ua-l53j0jwsua.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28178"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xhw67ZJiHnM6w7VhJZ0xa4j4CdRyJh0f/YiG0s0T63k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "l53j0jwsua"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz"}, {"Name": "integrity", "Value": "sha256-xhw67ZJiHnM6w7VhJZ0xa4j4CdRyJh0f/YiG0s0T63k="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.xy033osspr.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\zgyeaunyzm-xy033osspr.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000026304022"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "38016"}, {"Name": "ETag", "Value": "\"kIioyvmf/tLdemeqRPWlzFgcfoZ1Ove/FdckmYDBXpk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"V9VHrfWTQdDZhpDjgAzkkk7JIEn/cx3lMtjiDUXul/Q=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xy033osspr"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js"}, {"Name": "integrity", "Value": "sha256-V9VHrfWTQdDZhpDjgAzkkk7JIEn/cx3lMtjiDUXul/Q="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.xy033osspr.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "157850"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"V9VHrfWTQdDZhpDjgAzkkk7JIEn/cx3lMtjiDUXul/Q=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xy033osspr"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js"}, {"Name": "integrity", "Value": "sha256-V9VHrfWTQdDZhpDjgAzkkk7JIEn/cx3lMtjiDUXul/Q="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.xy033osspr.js.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\zgyeaunyzm-xy033osspr.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "38016"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kIioyvmf/tLdemeqRPWlzFgcfoZ1Ove/FdckmYDBXpk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xy033osspr"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.gz"}, {"Name": "integrity", "Value": "sha256-kIioyvmf/tLdemeqRPWlzFgcfoZ1Ove/FdckmYDBXpk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\oac7z3pbsy-1jo1kezdci.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000025732740"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "38860"}, {"Name": "ETag", "Value": "\"G3hEVXOXq2YI9Z7/yvAQvvf6j7gWnCvdHiIIKSi826M=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"k9Hcnfw1uBvIz3LBQJE2enuHAQ+2vcGQx7FY/sjMCT0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k9Hcnfw1uBvIz3LBQJE2enuHAQ+2vcGQx7FY/sjMCT0="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "167465"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"k9Hcnfw1uBvIz3LBQJE2enuHAQ+2vcGQx7FY/sjMCT0=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k9Hcnfw1uBvIz3LBQJE2enuHAQ+2vcGQx7FY/sjMCT0="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\oac7z3pbsy-1jo1kezdci.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "38860"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"G3hEVXOXq2YI9Z7/yvAQvvf6j7gWnCvdHiIIKSi826M=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G3hEVXOXq2YI9Z7/yvAQvvf6j7gWnCvdHiIIKSi826M="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\lqmvts9dba-xxsbxg4q4i.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015520720"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64429"}, {"Name": "ETag", "Value": "\"gQXErDzWkQZSfjEczQ6fcQDBKpT0yn/vH7j1YSexA0c=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"u9e0mnJh0mj/d32Mx6N9Ibj/0+LtjionqG5TWUyotPk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u9e0mnJh0mj/d32Mx6N9Ibj/0+LtjionqG5TWUyotPk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "306606"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"u9e0mnJh0mj/d32Mx6N9Ibj/0+LtjionqG5TWUyotPk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u9e0mnJh0mj/d32Mx6N9Ibj/0+LtjionqG5TWUyotPk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\lqmvts9dba-xxsbxg4q4i.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64429"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gQXErDzWkQZSfjEczQ6fcQDBKpT0yn/vH7j1YSexA0c=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gQXErDzWkQZSfjEczQ6fcQDBKpT0yn/vH7j1YSexA0c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.xxsbxg4q4i.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\lqmvts9dba-xxsbxg4q4i.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015520720"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64429"}, {"Name": "ETag", "Value": "\"gQXErDzWkQZSfjEczQ6fcQDBKpT0yn/vH7j1YSexA0c=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"u9e0mnJh0mj/d32Mx6N9Ibj/0+LtjionqG5TWUyotPk=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xxsbxg4q4i"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map"}, {"Name": "integrity", "Value": "sha256-u9e0mnJh0mj/d32Mx6N9Ibj/0+LtjionqG5TWUyotPk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.xxsbxg4q4i.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "306606"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"u9e0mnJh0mj/d32Mx6N9Ibj/0+LtjionqG5TWUyotPk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xxsbxg4q4i"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map"}, {"Name": "integrity", "Value": "sha256-u9e0mnJh0mj/d32Mx6N9Ibj/0+LtjionqG5TWUyotPk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.xxsbxg4q4i.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\lqmvts9dba-xxsbxg4q4i.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64429"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gQXErDzWkQZSfjEczQ6fcQDBKpT0yn/vH7j1YSexA0c=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xxsbxg4q4i"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map.gz"}, {"Name": "integrity", "Value": "sha256-gQXErDzWkQZSfjEczQ6fcQDBKpT0yn/vH7j1YSexA0c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.iug4zs40ca.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\6f8bul21g6-iug4zs40ca.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038056095"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26276"}, {"Name": "ETag", "Value": "\"4MKp1Hk7iiMTqFAZoVE172eMhUFb9P2egSWFw3YPP4k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"tp7hlG6xiG3KkONtF2Z5esr5FTcW2r83xX3hex8WMX8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iug4zs40ca"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js"}, {"Name": "integrity", "Value": "sha256-tp7hlG6xiG3KkONtF2Z5esr5FTcW2r83xX3hex8WMX8="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.iug4zs40ca.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "78199"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tp7hlG6xiG3KkONtF2Z5esr5FTcW2r83xX3hex8WMX8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iug4zs40ca"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js"}, {"Name": "integrity", "Value": "sha256-tp7hlG6xiG3KkONtF2Z5esr5FTcW2r83xX3hex8WMX8="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.iug4zs40ca.js.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\6f8bul21g6-iug4zs40ca.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26276"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4MKp1Hk7iiMTqFAZoVE172eMhUFb9P2egSWFw3YPP4k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iug4zs40ca"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.gz"}, {"Name": "integrity", "Value": "sha256-4MKp1Hk7iiMTqFAZoVE172eMhUFb9P2egSWFw3YPP4k="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\6f8bul21g6-iug4zs40ca.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038056095"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26276"}, {"Name": "ETag", "Value": "\"4MKp1Hk7iiMTqFAZoVE172eMhUFb9P2egSWFw3YPP4k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"tp7hlG6xiG3KkONtF2Z5esr5FTcW2r83xX3hex8WMX8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tp7hlG6xiG3KkONtF2Z5esr5FTcW2r83xX3hex8WMX8="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "78199"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tp7hlG6xiG3KkONtF2Z5esr5FTcW2r83xX3hex8WMX8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tp7hlG6xiG3KkONtF2Z5esr5FTcW2r83xX3hex8WMX8="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\6f8bul21g6-iug4zs40ca.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26276"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4MKp1Hk7iiMTqFAZoVE172eMhUFb9P2egSWFw3YPP4k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4MKp1Hk7iiMTqFAZoVE172eMhUFb9P2egSWFw3YPP4k="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.knl5pustf3.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\1sqahdjw2w-knl5pustf3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017904462"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55851"}, {"Name": "ETag", "Value": "\"nCVT1+BjbwsRwzOPpN1ZSa/iEC2jTv4WkLCtAvNJhLY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"IBIemhxt8rrB7DVR6DoN0KwZECFkw69mvMSn40GJXsc=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "knl5pustf3"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map"}, {"Name": "integrity", "Value": "sha256-IBIemhxt8rrB7DVR6DoN0KwZECFkw69mvMSn40GJXsc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.knl5pustf3.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "220561"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"IBIemhxt8rrB7DVR6DoN0KwZECFkw69mvMSn40GJXsc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "knl5pustf3"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map"}, {"Name": "integrity", "Value": "sha256-IBIemhxt8rrB7DVR6DoN0KwZECFkw69mvMSn40GJXsc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.knl5pustf3.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\1sqahdjw2w-knl5pustf3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55851"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"nCVT1+BjbwsRwzOPpN1ZSa/iEC2jTv4WkLCtAvNJhLY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "knl5pustf3"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz"}, {"Name": "integrity", "Value": "sha256-nCVT1+BjbwsRwzOPpN1ZSa/iEC2jTv4WkLCtAvNJhLY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\1sqahdjw2w-knl5pustf3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017904462"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55851"}, {"Name": "ETag", "Value": "\"nCVT1+BjbwsRwzOPpN1ZSa/iEC2jTv4WkLCtAvNJhLY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"IBIemhxt8rrB7DVR6DoN0KwZECFkw69mvMSn40GJXsc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IBIemhxt8rrB7DVR6DoN0KwZECFkw69mvMSn40GJXsc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "220561"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"IBIemhxt8rrB7DVR6DoN0KwZECFkw69mvMSn40GJXsc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IBIemhxt8rrB7DVR6DoN0KwZECFkw69mvMSn40GJXsc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\1sqahdjw2w-knl5pustf3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55851"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"nCVT1+BjbwsRwzOPpN1ZSa/iEC2jTv4WkLCtAvNJhLY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nCVT1+BjbwsRwzOPpN1ZSa/iEC2jTv4WkLCtAvNJhLY="}]}, {"Route": "TaskFlow.Web.bundle.scp.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\t9yymp5818-khv3du26ro.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000574052813"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1741"}, {"Name": "ETag", "Value": "\"2bj+yQE2YI1QzLnFdJ8MIsiCxthylp2AwB1vfqLMHdY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ="}]}, {"Route": "TaskFlow.Web.bundle.scp.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\TaskFlow.Web.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5796"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ="}]}, {"Route": "TaskFlow.Web.bundle.scp.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\t9yymp5818-khv3du26ro.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1741"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2bj+yQE2YI1QzLnFdJ8MIsiCxthylp2AwB1vfqLMHdY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2bj+yQE2YI1QzLnFdJ8MIsiCxthylp2AwB1vfqLMHdY="}]}, {"Route": "TaskFlow.Web.khv3du26ro.bundle.scp.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\t9yymp5818-khv3du26ro.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000574052813"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1741"}, {"Name": "ETag", "Value": "\"2bj+yQE2YI1QzLnFdJ8MIsiCxthylp2AwB1vfqLMHdY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "khv3du26ro"}, {"Name": "label", "Value": "TaskFlow.Web.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ="}]}, {"Route": "TaskFlow.Web.khv3du26ro.bundle.scp.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\TaskFlow.Web.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5796"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "khv3du26ro"}, {"Name": "label", "Value": "TaskFlow.Web.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ="}]}, {"Route": "TaskFlow.Web.khv3du26ro.bundle.scp.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\t9yymp5818-khv3du26ro.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1741"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2bj+yQE2YI1QzLnFdJ8MIsiCxthylp2AwB1vfqLMHdY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "khv3du26ro"}, {"Name": "label", "Value": "TaskFlow.Web.bundle.scp.css.gz"}, {"Name": "integrity", "Value": "sha256-2bj+yQE2YI1QzLnFdJ8MIsiCxthylp2AwB1vfqLMHdY="}]}, {"Route": "TaskFlow.Web.khv3du26ro.styles.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\o0dcj0py4z-khv3du26ro.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000574052813"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1741"}, {"Name": "ETag", "Value": "\"2bj+yQE2YI1QzLnFdJ8MIsiCxthylp2AwB1vfqLMHdY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "khv3du26ro"}, {"Name": "label", "Value": "TaskFlow.Web.styles.css"}, {"Name": "integrity", "Value": "sha256-KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ="}]}, {"Route": "TaskFlow.Web.khv3du26ro.styles.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\TaskFlow.Web.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5796"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "khv3du26ro"}, {"Name": "label", "Value": "TaskFlow.Web.styles.css"}, {"Name": "integrity", "Value": "sha256-KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ="}]}, {"Route": "TaskFlow.Web.khv3du26ro.styles.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\o0dcj0py4z-khv3du26ro.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1741"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2bj+yQE2YI1QzLnFdJ8MIsiCxthylp2AwB1vfqLMHdY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "khv3du26ro"}, {"Name": "label", "Value": "TaskFlow.Web.styles.css.gz"}, {"Name": "integrity", "Value": "sha256-2bj+yQE2YI1QzLnFdJ8MIsiCxthylp2AwB1vfqLMHdY="}]}, {"Route": "TaskFlow.Web.styles.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\o0dcj0py4z-khv3du26ro.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000574052813"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1741"}, {"Name": "ETag", "Value": "\"2bj+yQE2YI1QzLnFdJ8MIsiCxthylp2AwB1vfqLMHdY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ="}]}, {"Route": "TaskFlow.Web.styles.css", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\TaskFlow.Web.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5796"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ="}]}, {"Route": "TaskFlow.Web.styles.css.gz", "AssetFile": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\o0dcj0py4z-khv3du26ro.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1741"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2bj+yQE2YI1QzLnFdJ8MIsiCxthylp2AwB1vfqLMHdY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2bj+yQE2YI1QzLnFdJ8MIsiCxthylp2AwB1vfqLMHdY="}]}]}