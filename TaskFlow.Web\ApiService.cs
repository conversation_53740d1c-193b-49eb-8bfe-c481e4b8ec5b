﻿// TaskFlow.Web/ApiService.cs
using System.Net.Http.Json;
using TaskFlow.Web.Models; // You might need to adjust this namespace or copy models

public class ApiService
{
    private readonly HttpClient _httpClient;

    public ApiService(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    // Project methods
    public async Task<List<Project>?> GetProjectsAsync() =>
        await _httpClient.GetFromJsonAsync<List<Project>>("api/projects");

    public async Task<Project?> GetProjectAsync(int id) =>
        await _httpClient.GetFromJsonAsync<Project>($"api/projects/{id}");

    public async Task<Project?> CreateProjectAsync(Project project)
    {
        var response = await _httpClient.PostAsJsonAsync("api/projects", project);
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadFromJsonAsync<Project>();
    }
    public async Task UpdateProjectAsync(int id, Project project)
    {
        var response = await _httpClient.PutAsJsonAsync($"api/projects/{id}", project);
        response.EnsureSuccessStatusCode();
    }

    public async Task DeleteProjectAsync(int id)
    {
        var response = await _httpClient.DeleteAsync($"api/projects/{id}");
        response.EnsureSuccessStatusCode();
    }


    // TaskItem methods
    public async Task<List<TaskItem>?> GetTasksForProjectAsync(int projectId) =>
        await _httpClient.GetFromJsonAsync<List<TaskItem>>($"api/tasks/project/{projectId}");

    public async Task<TaskItem?> GetTaskAsync(int id) =>
        await _httpClient.GetFromJsonAsync<TaskItem>($"api/tasks/{id}");

    public async Task<TaskItem?> CreateTaskAsync(TaskItem task)
    {
        var response = await _httpClient.PostAsJsonAsync("api/tasks", task);
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadFromJsonAsync<TaskItem>();
    }

    public async Task<TaskItem?> UpdateTaskAsync(int id, TaskItem task)
    {
        var response = await _httpClient.PutAsJsonAsync($"api/tasks/{id}", task);
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadFromJsonAsync<TaskItem>();
    }

    public async Task<TaskItem?> UpdateTaskStatusAsync(int taskId, TaskFlow.Web.Models.TaskStatus newStatus)
    {
        // The API endpoint expects TaskStatus in the body, not as a query param.
        // For simplicity, let's assume the API is adjusted or we send a simple object.
        // A better API might be PUT /api/tasks/{id}/status with { "newStatus": "InProgress" }
        // For now, let's assume the API takes it as a simple JSON value in the body.
        var response = await _httpClient.PutAsJsonAsync($"api/tasks/{taskId}/status", newStatus);
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadFromJsonAsync<TaskItem>();
    }


    public async Task DeleteTaskAsync(int id)
    {
        var response = await _httpClient.DeleteAsync($"api/tasks/{id}");
        response.EnsureSuccessStatusCode();
    }

    // Sprint methods
    public async Task<List<Sprint>?> GetSprintsForProjectAsync(int projectId) =>
        await _httpClient.GetFromJsonAsync<List<Sprint>>($"api/sprints/project/{projectId}");

    public async Task<Sprint?> CreateSprintAsync(Sprint sprint)
    {
        var response = await _httpClient.PostAsJsonAsync("api/sprints", sprint);
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadFromJsonAsync<Sprint>();
    }
    // Add UpdateSprintAsync, DeleteSprintAsync as needed
}
