﻿@page "/project/{ProjectId:int}"
@using TaskFlow.Web.Models
@inject ApiService ApiClient
@inject NavigationManager NavigationManager
@rendermode InteractiveServer

<h3>Project Board: @project?.Name</h3>

@if (project is null)
{
    <p><em>Loading project details...</em></p>
}
else
{
    <div class="mb-3">
        <button @onclick="ShowCreateTaskModal">Add New Task</button>
        @* Add Sprint Management UI here if desired *@
    </div>

    <div class="kanban-board">
        @foreach (var status in Enum.GetValues<Models.TaskStatus>())
        {
            <div class="kanban-column"
                 @ondragover="HandleDragOver"
                 @ondragover:preventDefault="true"
                 @ondrop="@(async () => await HandleDrop(status))"
                 data-status="@status">
                <h4>@GetColumnTitle(status)</h4>
                @foreach (var task in GetTasksByStatus(status))
                {
                    <div class="kanban-task"
                         draggable="true"
                         @ondragstart="@(() => HandleDragStart(task))">
                        <strong>@task.Title</strong>
                        <p>@task.Description</p>
                        <small>Priority: @task.Priority</small><br />
                        @if (task.DueDate.HasValue)
                        {
                            <small>Due: @task.DueDate.Value.ToShortDateString()</small>
                        }
                        @* Add edit/delete task buttons *@
                    </div>
                }
            </div>
        }
    </div>
}

@if (showCreateTaskModal)
{
    <div class="modal" tabindex="-1" style="display:block;" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create Task</h5>
                    <button type="button" class="btn-close" @onclick="CloseCreateTaskModal"></button>
                </div>
                <div class="modal-body">
                    <EditForm Model="newTask" OnValidSubmit="HandleCreateTask">
                        <DataAnnotationsValidator />
                        <div class="mb-3">
                            <label class="form-label">Title:</label>
                            <InputText class="form-control" @bind-Value="newTask.Title" />
                            <ValidationMessage For="@(() => newTask.Title)" />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description:</label>
                            <InputTextArea class="form-control" @bind-Value="newTask.Description" />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Priority:</label>
                            <InputSelect class="form-control" @bind-Value="newTask.Priority">
                                @foreach (var priority in Enum.GetValues<TaskPriority>())
                                {
                                    <option value="@priority">@priority</option>
                                }
                            </InputSelect>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Due Date:</label>
                            <InputDate class="form-control" @bind-Value="newTask.DueDate" />
                        </div>
                        @* Add Sprint selection dropdown if sprints are implemented *@
                        <button type="submit" class="btn btn-primary">Save Task</button>
                    </EditForm>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
}


<style>
    .kanban-board {
        display: flex;
        gap: 10px;
    }

    .kanban-column {
        flex: 1;
        background-color: #f0f0f0;
        padding: 10px;
        border-radius: 5px;
        min-height: 300px;
    }

    .kanban-task {
        background-color: white;
        padding: 8px;
        margin-bottom: 8px;
        border: 1px solid #ccc;
        border-radius: 3px;
        cursor: grab;
    }
</style>

@code {
    [Parameter]
    public int ProjectId { get; set; }

    private Project? project;
    private List<TaskItem> tasks = new();
    private TaskItem? draggedTask;

    private bool showCreateTaskModal = false;
    private TaskItem newTask = new();


    protected override async Task OnInitializedAsync()
    {
        await LoadProjectData();
    }

    private async Task LoadProjectData()
    {
        project = await ApiClient.GetProjectAsync(ProjectId);
        if (project != null)
        {
            tasks = await ApiClient.GetTasksForProjectAsync(ProjectId) ?? new List<TaskItem>();
        }
        else
        {
            NavigationManager.NavigateTo("/projects"); // Or show error
        }
        StateHasChanged();
    }

    private IEnumerable<TaskItem> GetTasksByStatus(Models.TaskStatus status)
    {
        return tasks.Where(t => t.Status == status).OrderBy(t => t.Priority).ThenBy(t => t.DueDate);
    }

    private string GetColumnTitle(Models.TaskStatus status)
    {
        return status switch
        {
            Models.TaskStatus.NotStarted => "Not Started",
            Models.TaskStatus.InProgress => "In Progress",
            Models.TaskStatus.Finished => "Finished",
            _ => "Unknown"
        };
    }

    private void HandleDragStart(TaskItem task)
    {
        draggedTask = task;
    }

    private void HandleDragOver()
    {
        // This method is necessary to make ondrop work.
        // You can add logic here if needed (e.g., visual feedback).
    }

    private async Task HandleDrop(Models.TaskStatus targetStatus)
    {
        if (draggedTask != null && draggedTask.Status != targetStatus)
        {
            var originalStatus = draggedTask.Status;
            draggedTask.Status = targetStatus; // Optimistic update for UI responsiveness

            var updatedTask = await ApiClient.UpdateTaskStatusAsync(draggedTask.Id, targetStatus);
            if (updatedTask != null)
            {
                // Find and update the task in the local list
                var taskInList = tasks.FirstOrDefault(t => t.Id == draggedTask.Id);
                if (taskInList != null)
                {
                    taskInList.Status = updatedTask.Status;
                    taskInList.UpdatedAt = updatedTask.UpdatedAt;
                }
            }
            else
            {
                // Revert optimistic update on failure
                draggedTask.Status = originalStatus;
                // Optionally show an error message
            }
            draggedTask = null; // Reset dragged task
            StateHasChanged(); // Re-render
        }
    }

    private void ShowCreateTaskModal()
    {
        newTask = new TaskItem { ProjectId = ProjectId, Status = Models.TaskStatus.NotStarted };
        showCreateTaskModal = true;
    }

    private void CloseCreateTaskModal() => showCreateTaskModal = false;

    private async Task HandleCreateTask()
    {
        if (project == null) return; // Should not happen

        newTask.ProjectId = ProjectId;
        var created = await ApiClient.CreateTaskAsync(newTask);
        if (created != null)
        {
            tasks.Add(created);
            CloseCreateTaskModal();
            StateHasChanged();
        }
        // Else: handle error
    }
}
