is_global = true
build_property.TargetFramework = net9.0
build_property.TargetFrameworkIdentifier = .NETCoreApp
build_property.TargetFrameworkVersion = v9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = TaskFlow.Web
build_property.RootNamespace = TaskFlow.Web
build_property.ProjectDir = C:\programok\TaskFlow\TaskFlow.Web\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = C:\programok\TaskFlow\TaskFlow.Web
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[C:/programok/TaskFlow/TaskFlow.Web/Components/App.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xBcHAucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/programok/TaskFlow/TaskFlow.Web/Components/Pages/Counter.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xDb3VudGVyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/programok/TaskFlow/TaskFlow.Web/Components/Pages/Error.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xFcnJvci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/programok/TaskFlow/TaskFlow.Web/Components/Pages/Home.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xIb21lLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/programok/TaskFlow/TaskFlow.Web/Components/Pages/ProjectBoard.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xQcm9qZWN0Qm9hcmQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/programok/TaskFlow/TaskFlow.Web/Components/Pages/Projects.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xQcm9qZWN0cy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/programok/TaskFlow/TaskFlow.Web/Components/Pages/Weather.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xXZWF0aGVyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/programok/TaskFlow/TaskFlow.Web/Components/Routes.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xSb3V0ZXMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/programok/TaskFlow/TaskFlow.Web/Components/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xfSW1wb3J0cy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/programok/TaskFlow/TaskFlow.Web/Components/Layout/MainLayout.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xMYXlvdXRcTWFpbkxheW91dC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = b-ksu9neqinx

[C:/programok/TaskFlow/TaskFlow.Web/Components/Layout/NavMenu.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xMYXlvdXRcTmF2TWVudS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = b-m6rwse0obz
