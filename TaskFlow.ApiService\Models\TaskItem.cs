﻿// TaskFlow.ApiService/Models/TaskItem.cs
using System.ComponentModel.DataAnnotations;

namespace TaskFlow.ApiService.Models;

public enum TaskStatus
{
    NotStarted,
    InProgress,
    Finished
}

public enum TaskPriority
{
    Low,
    Medium,
    High
}

public class TaskItem
{
    public int Id { get; set; }

    [Required]
    [StringLength(200)]
    public string Title { get; set; } = string.Empty;

    public string? Description { get; set; }
    public TaskStatus Status { get; set; } = TaskStatus.NotStarted;
    public TaskPriority Priority { get; set; } = TaskPriority.Medium;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? DueDate { get; set; }

    public int ProjectId { get; set; }
    public Project? Project { get; set; }

    public int? SprintId { get; set; }
    public Sprint? Sprint { get; set; }
}
