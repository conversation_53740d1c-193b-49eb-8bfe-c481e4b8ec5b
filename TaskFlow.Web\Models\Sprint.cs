// TaskFlow.ApiService/Models/Sprint.cs
using System.ComponentModel.DataAnnotations;

namespace TaskFlow.Web.Models;

public class Sprint
{
    public int Id { get; set; }

    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public bool IsActive { get; set; } = false; // Or calculate based on dates

    public int ProjectId { get; set; }
    public Project? Project { get; set; }
    public List<TaskItem> Tasks { get; set; } = [];
}
