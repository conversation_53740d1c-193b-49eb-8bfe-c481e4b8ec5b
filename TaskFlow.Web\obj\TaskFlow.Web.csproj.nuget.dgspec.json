{"format": 1, "restore": {"C:\\programok\\TaskFlow\\TaskFlow.Web\\TaskFlow.Web.csproj": {}}, "projects": {"C:\\programok\\TaskFlow\\TaskFlow.ServiceDefaults\\TaskFlow.ServiceDefaults.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\programok\\TaskFlow\\TaskFlow.ServiceDefaults\\TaskFlow.ServiceDefaults.csproj", "projectName": "TaskFlow.ServiceDefaults", "projectPath": "C:\\programok\\TaskFlow\\TaskFlow.ServiceDefaults\\TaskFlow.ServiceDefaults.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\programok\\TaskFlow\\TaskFlow.ServiceDefaults\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "CentralPackageTransitivePinningEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"noWarn": ["NU1507"], "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.Http.Resilience": {"target": "Package", "version": "[9.5.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.ServiceDiscovery": {"target": "Package", "version": "[9.3.0, )", "versionCentrallyManaged": true}, "OpenTelemetry.Exporter.OpenTelemetryProtocol": {"target": "Package", "version": "[1.9.0, )", "versionCentrallyManaged": true}, "OpenTelemetry.Extensions.Hosting": {"target": "Package", "version": "[1.9.0, )", "versionCentrallyManaged": true}, "OpenTelemetry.Instrumentation.AspNetCore": {"target": "Package", "version": "[1.12.0, )", "versionCentrallyManaged": true}, "OpenTelemetry.Instrumentation.Http": {"target": "Package", "version": "[1.12.0, )", "versionCentrallyManaged": true}, "OpenTelemetry.Instrumentation.Runtime": {"target": "Package", "version": "[1.9.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"Aspire.Hosting.AppHost": "9.3.0", "Aspire.Hosting.Testing": "9.3.0", "coverlet.collector": "6.0.2", "Microsoft.AspNetCore.OpenApi": "9.0.0", "Microsoft.EntityFrameworkCore": "9.0.5", "Microsoft.EntityFrameworkCore.Design": "9.0.5", "Microsoft.EntityFrameworkCore.Sqlite": "9.0.5", "Microsoft.Extensions.Http.Resilience": "9.5.0", "Microsoft.Extensions.ServiceDiscovery": "9.3.0", "Microsoft.NET.Test.Sdk": "17.10.0", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.9.0", "OpenTelemetry.Extensions.Hosting": "1.9.0", "OpenTelemetry.Instrumentation.AspNetCore": "1.12.0", "OpenTelemetry.Instrumentation.Http": "1.12.0", "OpenTelemetry.Instrumentation.Runtime": "1.9.0", "Scalar.AspNetCore": "2.4.3", "Swashbuckle.AspNetCore": "8.1.2", "xunit": "2.9.0", "xunit.runner.visualstudio": "2.8.2"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\programok\\TaskFlow\\TaskFlow.Web\\TaskFlow.Web.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\programok\\TaskFlow\\TaskFlow.Web\\TaskFlow.Web.csproj", "projectName": "TaskFlow.Web", "projectPath": "C:\\programok\\TaskFlow\\TaskFlow.Web\\TaskFlow.Web.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "CentralPackageTransitivePinningEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\programok\\TaskFlow\\TaskFlow.ServiceDefaults\\TaskFlow.ServiceDefaults.csproj": {"projectPath": "C:\\programok\\TaskFlow\\TaskFlow.ServiceDefaults\\TaskFlow.ServiceDefaults.csproj"}}}}, "warningProperties": {"noWarn": ["NU1507"], "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "centralPackageVersions": {"Aspire.Hosting.AppHost": "9.3.0", "Aspire.Hosting.Testing": "9.3.0", "coverlet.collector": "6.0.2", "Microsoft.AspNetCore.OpenApi": "9.0.0", "Microsoft.EntityFrameworkCore": "9.0.5", "Microsoft.EntityFrameworkCore.Design": "9.0.5", "Microsoft.EntityFrameworkCore.Sqlite": "9.0.5", "Microsoft.Extensions.Http.Resilience": "9.5.0", "Microsoft.Extensions.ServiceDiscovery": "9.3.0", "Microsoft.NET.Test.Sdk": "17.10.0", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.9.0", "OpenTelemetry.Extensions.Hosting": "1.9.0", "OpenTelemetry.Instrumentation.AspNetCore": "1.12.0", "OpenTelemetry.Instrumentation.Http": "1.12.0", "OpenTelemetry.Instrumentation.Runtime": "1.9.0", "Scalar.AspNetCore": "2.4.3", "Swashbuckle.AspNetCore": "8.1.2", "xunit": "2.9.0", "xunit.runner.visualstudio": "2.8.2"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}