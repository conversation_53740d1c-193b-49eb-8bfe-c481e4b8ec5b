{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"TaskFlow.Tests/1.0.0": {"dependencies": {"Aspire.Hosting.Testing": "9.3.0", "Microsoft.NET.Test.Sdk": "17.10.0", "TaskFlow.AppHost": "1.0.0", "coverlet.collector": "6.0.2", "xunit": "2.9.0", "xunit.runner.visualstudio": "2.8.2"}, "runtime": {"TaskFlow.Tests.dll": {}}}, "Aspire.Dashboard.Sdk.win-x64/9.3.0": {}, "Aspire.Hosting/9.3.0": {"dependencies": {"AspNetCore.HealthChecks.Uris": "9.0.0", "Google.Protobuf": "3.30.2", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "16.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Configuration.Binder": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.4", "Microsoft.Extensions.Hosting": "9.0.4", "Microsoft.Extensions.Hosting.Abstractions": "9.0.5", "Microsoft.Extensions.Http": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.5.2", "StreamJsonRpc": "2.21.69", "System.IO.Hashing": "9.0.4"}, "runtime": {"lib/net8.0/Aspire.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "9.300.25.26520"}}, "resources": {"lib/net8.0/cs/Aspire.Hosting.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Aspire.Hosting.resources.dll": {"locale": "de"}, "lib/net8.0/es/Aspire.Hosting.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Aspire.Hosting.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Aspire.Hosting.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Aspire.Hosting.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Aspire.Hosting.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Aspire.Hosting.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Aspire.Hosting.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Aspire.Hosting.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Aspire.Hosting.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Aspire.Hosting.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Aspire.Hosting.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Aspire.Hosting.AppHost/9.3.0": {"dependencies": {"AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting": "9.3.0", "Google.Protobuf": "3.30.2", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "16.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Configuration.Binder": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.4", "Microsoft.Extensions.Hosting": "9.0.4", "Microsoft.Extensions.Hosting.Abstractions": "9.0.5", "Microsoft.Extensions.Http": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.5.2", "StreamJsonRpc": "2.21.69", "System.IO.Hashing": "9.0.4"}, "runtime": {"lib/net9.0/Aspire.Hosting.AppHost.dll": {"assemblyVersion": "*******", "fileVersion": "9.300.25.26520"}}}, "Aspire.Hosting.Orchestration.win-x64/9.3.0": {}, "Aspire.Hosting.Testing/9.3.0": {"dependencies": {"AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting": "9.3.0", "Aspire.Hosting.AppHost": "9.3.0", "Google.Protobuf": "3.30.2", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "16.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Configuration.Binder": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.4", "Microsoft.Extensions.Hosting": "9.0.4", "Microsoft.Extensions.Hosting.Abstractions": "9.0.5", "Microsoft.Extensions.Http": "9.0.5", "Microsoft.Extensions.Http.Resilience": "9.5.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.5.2", "Polly.Extensions": "8.5.2", "StreamJsonRpc": "2.21.69", "System.IO.Hashing": "9.0.4"}, "runtime": {"lib/net9.0/Aspire.Hosting.Testing.dll": {"assemblyVersion": "*******", "fileVersion": "9.300.25.26520"}}, "resources": {"lib/net9.0/cs/Aspire.Hosting.Testing.resources.dll": {"locale": "cs"}, "lib/net9.0/de/Aspire.Hosting.Testing.resources.dll": {"locale": "de"}, "lib/net9.0/es/Aspire.Hosting.Testing.resources.dll": {"locale": "es"}, "lib/net9.0/fr/Aspire.Hosting.Testing.resources.dll": {"locale": "fr"}, "lib/net9.0/it/Aspire.Hosting.Testing.resources.dll": {"locale": "it"}, "lib/net9.0/ja/Aspire.Hosting.Testing.resources.dll": {"locale": "ja"}, "lib/net9.0/ko/Aspire.Hosting.Testing.resources.dll": {"locale": "ko"}, "lib/net9.0/pl/Aspire.Hosting.Testing.resources.dll": {"locale": "pl"}, "lib/net9.0/pt-BR/Aspire.Hosting.Testing.resources.dll": {"locale": "pt-BR"}, "lib/net9.0/ru/Aspire.Hosting.Testing.resources.dll": {"locale": "ru"}, "lib/net9.0/tr/Aspire.Hosting.Testing.resources.dll": {"locale": "tr"}, "lib/net9.0/zh-Hans/Aspire.Hosting.Testing.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0/zh-Hant/Aspire.Hosting.Testing.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "AspNetCore.HealthChecks.Uris/9.0.0": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.4", "Microsoft.Extensions.Http": "9.0.5"}, "runtime": {"lib/net8.0/HealthChecks.Uris.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "coverlet.collector/6.0.2": {}, "Fractions/7.3.0": {"runtime": {"lib/netstandard2.1/Fractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Google.Protobuf/3.30.2": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Grpc.AspNetCore/2.71.0": {"dependencies": {"Google.Protobuf": "3.30.2", "Grpc.AspNetCore.Server.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0"}}, "Grpc.AspNetCore.Server/2.71.0": {"dependencies": {"Grpc.Net.Common": "2.71.0"}, "runtime": {"lib/net9.0/Grpc.AspNetCore.Server.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Grpc.AspNetCore.Server.ClientFactory/2.71.0": {"dependencies": {"Grpc.AspNetCore.Server": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0"}, "runtime": {"lib/net9.0/Grpc.AspNetCore.Server.ClientFactory.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Grpc.Core.Api/2.71.0": {"runtime": {"lib/netstandard2.1/Grpc.Core.Api.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Grpc.Net.Client/2.71.0": {"dependencies": {"Grpc.Net.Common": "2.71.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.5"}, "runtime": {"lib/net8.0/Grpc.Net.Client.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Grpc.Net.ClientFactory/2.71.0": {"dependencies": {"Grpc.Net.Client": "2.71.0", "Microsoft.Extensions.Http": "9.0.5"}, "runtime": {"lib/net8.0/Grpc.Net.ClientFactory.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Grpc.Net.Common/2.71.0": {"dependencies": {"Grpc.Core.Api": "2.71.0"}, "runtime": {"lib/net8.0/Grpc.Net.Common.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Grpc.Tools/2.72.0": {}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "Json.More.Net/2.1.0": {"runtime": {"lib/net9.0/Json.More.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.0.0"}}}, "JsonPatch.Net/3.3.0": {"dependencies": {"JsonPointer.Net": "5.2.0"}, "runtime": {"lib/net9.0/JsonPatch.Net.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.3.0.0"}}}, "JsonPointer.Net/5.2.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Json.More.Net": "2.1.0"}, "runtime": {"lib/net9.0/JsonPointer.Net.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.2.0.0"}}}, "KubernetesClient/16.0.7": {"dependencies": {"Fractions": "7.3.0", "YamlDotNet": "16.3.0"}, "runtime": {"lib/net9.0/KubernetesClient.dll": {"assemblyVersion": "********", "fileVersion": "16.0.7.57311"}}}, "MessagePack/2.5.192": {"dependencies": {"MessagePack.Annotations": "2.5.192", "Microsoft.NET.StringTools": "17.6.3"}, "runtime": {"lib/net6.0/MessagePack.dll": {"assemblyVersion": "2.5.0.0", "fileVersion": "2.5.192.54228"}}}, "MessagePack.Annotations/2.5.192": {"runtime": {"lib/netstandard2.0/MessagePack.Annotations.dll": {"assemblyVersion": "2.5.0.0", "fileVersion": "2.5.192.54228"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.CodeCoverage/17.10.0": {"runtime": {"lib/netcoreapp3.1/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {"assemblyVersion": "********", "fileVersion": "17.1000.424.16205"}}}, "Microsoft.Extensions.AmbientMetadata.Application/9.5.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Hosting.Abstractions": "9.0.5", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.dll": {"assemblyVersion": "*******", "fileVersion": "9.500.25.26209"}}}, "Microsoft.Extensions.Compliance.Abstractions/9.5.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.ObjectPool": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.500.25.26209"}}}, "Microsoft.Extensions.Configuration/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Configuration.CommandLine/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.5", "Microsoft.Extensions.FileProviders.Physical": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Configuration.Json/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Configuration.Json": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.5", "Microsoft.Extensions.FileProviders.Physical": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.5.0": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll": {"assemblyVersion": "*******", "fileVersion": "9.500.25.26209"}}}, "Microsoft.Extensions.Diagnostics/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.5", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.5.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll": {"assemblyVersion": "*******", "fileVersion": "9.500.25.26209"}}}, "Microsoft.Extensions.Diagnostics.HealthChecks/9.0.4": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "9.0.4", "Microsoft.Extensions.Hosting.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.HealthChecks.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16403"}}}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/9.0.4": {"runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16403"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.4": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.5", "Microsoft.Extensions.FileSystemGlobbing": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.4": {"runtime": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Hosting/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Configuration.Binder": "9.0.5", "Microsoft.Extensions.Configuration.CommandLine": "9.0.4", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.4", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.4", "Microsoft.Extensions.Configuration.Json": "9.0.4", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.4", "Microsoft.Extensions.DependencyInjection": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Diagnostics": "9.0.5", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.5", "Microsoft.Extensions.FileProviders.Physical": "9.0.4", "Microsoft.Extensions.Hosting.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Configuration": "9.0.5", "Microsoft.Extensions.Logging.Console": "9.0.4", "Microsoft.Extensions.Logging.Debug": "9.0.4", "Microsoft.Extensions.Logging.EventLog": "9.0.4", "Microsoft.Extensions.Logging.EventSource": "9.0.4", "Microsoft.Extensions.Options": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.5", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Http/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Diagnostics": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Http.Diagnostics/9.5.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.AutoActivation": "9.5.0", "Microsoft.Extensions.Http": "9.0.5", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.5", "Microsoft.Extensions.Telemetry": "9.5.0", "System.IO.Pipelines": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.500.25.26209"}}}, "Microsoft.Extensions.Http.Resilience/9.5.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.5", "Microsoft.Extensions.Http.Diagnostics": "9.5.0", "Microsoft.Extensions.ObjectPool": "9.0.5", "Microsoft.Extensions.Resilience": "9.5.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Resilience.dll": {"assemblyVersion": "*******", "fileVersion": "9.500.25.26209"}}}, "Microsoft.Extensions.Logging/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging.Configuration/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Configuration.Binder": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging.Console/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Configuration": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Logging.Debug/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Logging.EventLog/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "System.Diagnostics.EventLog": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Logging.EventSource/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.ObjectPool/9.0.5": {"runtime": {"lib/net9.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.22904"}}}, "Microsoft.Extensions.Options/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Configuration.Binder": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Primitives/9.0.5": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Resilience/9.5.0": {"dependencies": {"Microsoft.Extensions.Diagnostics": "9.0.5", "Microsoft.Extensions.Diagnostics.ExceptionSummarization": "9.5.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.5", "Microsoft.Extensions.Telemetry.Abstractions": "9.5.0", "Polly.Extensions": "8.5.2", "Polly.RateLimiting": "8.4.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Resilience.dll": {"assemblyVersion": "*******", "fileVersion": "9.500.25.26209"}}}, "Microsoft.Extensions.Telemetry/9.5.0": {"dependencies": {"Microsoft.Extensions.AmbientMetadata.Application": "9.5.0", "Microsoft.Extensions.DependencyInjection.AutoActivation": "9.5.0", "Microsoft.Extensions.Logging.Configuration": "9.0.5", "Microsoft.Extensions.ObjectPool": "9.0.5", "Microsoft.Extensions.Telemetry.Abstractions": "9.5.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.dll": {"assemblyVersion": "*******", "fileVersion": "9.500.25.26209"}}}, "Microsoft.Extensions.Telemetry.Abstractions/9.5.0": {"dependencies": {"Microsoft.Extensions.Compliance.Abstractions": "9.5.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.ObjectPool": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.500.25.26209"}}}, "Microsoft.NET.StringTools/17.6.3": {"runtime": {"lib/net7.0/Microsoft.NET.StringTools.dll": {"assemblyVersion": "*******", "fileVersion": "17.6.3.22601"}}}, "Microsoft.NET.Test.Sdk/17.10.0": {"dependencies": {"Microsoft.CodeCoverage": "17.10.0", "Microsoft.TestPlatform.TestHost": "17.10.0"}}, "Microsoft.TestPlatform.ObjectModel/17.10.0": {"runtime": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {"assemblyVersion": "********", "fileVersion": "17.1000.24.27212"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {"assemblyVersion": "********", "fileVersion": "17.1000.24.27212"}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {"assemblyVersion": "********", "fileVersion": "17.1000.24.27212"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.TestPlatform.TestHost/17.10.0": {"dependencies": {"Microsoft.TestPlatform.ObjectModel": "17.10.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CommunicationUtilities.dll": {"assemblyVersion": "********", "fileVersion": "17.1000.24.27212"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CrossPlatEngine.dll": {"assemblyVersion": "********", "fileVersion": "17.1000.24.27212"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.Utilities.dll": {"assemblyVersion": "********", "fileVersion": "17.1000.24.27212"}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.Common.dll": {"assemblyVersion": "********", "fileVersion": "17.1000.24.27212"}, "lib/netcoreapp3.1/testhost.dll": {"assemblyVersion": "********", "fileVersion": "17.1000.24.27212"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.VisualStudio.Threading.Only/17.13.61": {"dependencies": {"Microsoft.VisualStudio.Validation": "17.8.8"}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.Threading.dll": {"assemblyVersion": "17.13.0.0", "fileVersion": "17.13.61.36374"}}, "resources": {"lib/net8.0/cs/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.VisualStudio.Validation/17.8.8": {"runtime": {"lib/net6.0/Microsoft.VisualStudio.Validation.dll": {"assemblyVersion": "17.8.0.0", "fileVersion": "17.8.8.15457"}}, "resources": {"lib/net6.0/cs/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Nerdbank.Streams/2.11.90": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.VisualStudio.Threading.Only": "17.13.61", "Microsoft.VisualStudio.Validation": "17.8.8"}, "runtime": {"lib/net6.0/Nerdbank.Streams.dll": {"assemblyVersion": "2.11.0.0", "fileVersion": "2.11.90.55113"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "Polly.Core/8.5.2": {"runtime": {"lib/net8.0/Polly.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.5.2.4319"}}}, "Polly.Extensions/8.5.2": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Polly.Core": "8.5.2"}, "runtime": {"lib/net8.0/Polly.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.5.2.4319"}}}, "Polly.RateLimiting/8.4.2": {"dependencies": {"Polly.Core": "8.5.2", "System.Threading.RateLimiting": "8.0.0"}, "runtime": {"lib/net8.0/Polly.RateLimiting.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "StreamJsonRpc/2.21.69": {"dependencies": {"MessagePack": "2.5.192", "Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.VisualStudio.Threading.Only": "17.13.61", "Microsoft.VisualStudio.Validation": "17.8.8", "Nerdbank.Streams": "2.11.90", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net8.0/StreamJsonRpc.dll": {"assemblyVersion": "2.21.0.0", "fileVersion": "2.21.69.19811"}}, "resources": {"lib/net8.0/cs/StreamJsonRpc.resources.dll": {"locale": "cs"}, "lib/net8.0/de/StreamJsonRpc.resources.dll": {"locale": "de"}, "lib/net8.0/es/StreamJsonRpc.resources.dll": {"locale": "es"}, "lib/net8.0/fr/StreamJsonRpc.resources.dll": {"locale": "fr"}, "lib/net8.0/it/StreamJsonRpc.resources.dll": {"locale": "it"}, "lib/net8.0/ja/StreamJsonRpc.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/StreamJsonRpc.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/StreamJsonRpc.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/StreamJsonRpc.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/StreamJsonRpc.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/StreamJsonRpc.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/StreamJsonRpc.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/StreamJsonRpc.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Diagnostics.EventLog/9.0.4": {"runtime": {"lib/net9.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.IO.Hashing/9.0.4": {"runtime": {"lib/net9.0/System.IO.Hashing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.IO.Pipelines/9.0.5": {"runtime": {"lib/net9.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.Threading.RateLimiting/8.0.0": {}, "xunit/2.9.0": {"dependencies": {"xunit.analyzers": "1.15.0", "xunit.assert": "2.9.0", "xunit.core": "2.9.0"}}, "xunit.abstractions/2.0.3": {"runtime": {"lib/netstandard2.0/xunit.abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "xunit.analyzers/1.15.0": {}, "xunit.assert/2.9.0": {"runtime": {"lib/net6.0/xunit.assert.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "xunit.core/2.9.0": {"dependencies": {"xunit.extensibility.core": "2.9.0", "xunit.extensibility.execution": "2.9.0"}}, "xunit.extensibility.core/2.9.0": {"dependencies": {"xunit.abstractions": "2.0.3"}, "runtime": {"lib/netstandard1.1/xunit.core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "xunit.extensibility.execution/2.9.0": {"dependencies": {"xunit.extensibility.core": "2.9.0"}, "runtime": {"lib/netstandard1.1/xunit.execution.dotnet.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "xunit.runner.visualstudio/2.8.2": {}, "YamlDotNet/16.3.0": {"runtime": {"lib/net8.0/YamlDotNet.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "TaskFlow.AppHost/1.0.0": {"dependencies": {"Aspire.Dashboard.Sdk.win-x64": "9.3.0", "Aspire.Hosting.AppHost": "9.3.0", "Aspire.Hosting.Orchestration.win-x64": "9.3.0"}, "runtime": {"TaskFlow.AppHost.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"TaskFlow.Tests/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Aspire.Dashboard.Sdk.win-x64/9.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-XWe/PW2MkZbVb6UwfeXrFp8+oG+RzckZti8OgKqhg5DwQ+3OykTu7fonz2EvLuOKVqHcW3121oxWc0ulTNmRhQ==", "path": "aspire.dashboard.sdk.win-x64/9.3.0", "hashPath": "aspire.dashboard.sdk.win-x64.9.3.0.nupkg.sha512"}, "Aspire.Hosting/9.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-y9MYFzHo87H9OI8/GUjzekiMJf2uVAVJROv6xiuPa//AMFvkP2W5ELkcc3/FxY0EW2EX2erkqTeDE3uhw6P/pQ==", "path": "aspire.hosting/9.3.0", "hashPath": "aspire.hosting.9.3.0.nupkg.sha512"}, "Aspire.Hosting.AppHost/9.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/nNngW76y65NWOofCB+/t1bDZCacPIhBk9YZVygHlnBZaZS8d1CbLxQF4zMDQucQeaaY5BFBQUAtcc3PGLxHjA==", "path": "aspire.hosting.apphost/9.3.0", "hashPath": "aspire.hosting.apphost.9.3.0.nupkg.sha512"}, "Aspire.Hosting.Orchestration.win-x64/9.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-MhQMIdyTECjH3pclqGU2dDND6YzGCz853xO6YO4NXkw5L6wEJUuZYMNx5Tb8cBJiQUJ/UjGod79hvVOxcfXocw==", "path": "aspire.hosting.orchestration.win-x64/9.3.0", "hashPath": "aspire.hosting.orchestration.win-x64.9.3.0.nupkg.sha512"}, "Aspire.Hosting.Testing/9.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kuXFuNltBEif0DH6REc6FXiZDl6Pjnu7+rc05TMtjJ03yPMKuHfsn8CyoFjbZD+BoMJhdR2Ws8KyZRSxJLEyMA==", "path": "aspire.hosting.testing/9.3.0", "hashPath": "aspire.hosting.testing.9.3.0.nupkg.sha512"}, "AspNetCore.HealthChecks.Uris/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-XYdNlA437KeF8p9qOpZFyNqAN+c0FXt/JjTvzH/Qans0q0O3pPE8KPnn39ucQQjR/Roum1vLTP3kXiUs8VHyuA==", "path": "aspnetcore.healthchecks.uris/9.0.0", "hashPath": "aspnetcore.healthchecks.uris.9.0.0.nupkg.sha512"}, "coverlet.collector/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-bJShQ6uWRTQ100ZeyiMqcFlhP7WJ+bCuabUs885dJiBEzMsJMSFr7BOyeCw4rgvQokteGi5rKQTlkhfQPUXg2A==", "path": "coverlet.collector/6.0.2", "hashPath": "coverlet.collector.6.0.2.nupkg.sha512"}, "Fractions/7.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-2bETFWLBc8b7Ut2SVi+bxhGVwiSpknHYGBh2PADyGWONLkTxT7bKyDRhF8ao+XUv90tq8Fl7GTPxSI5bacIRJw==", "path": "fractions/7.3.0", "hashPath": "fractions.7.3.0.nupkg.sha512"}, "Google.Protobuf/3.30.2": {"type": "package", "serviceable": true, "sha512": "sha512-Y2aOVLIt75yeeEWigg9V9YnjsEm53sADtLGq0gLhwaXpk3iu8tYSoauolyhenagA2sWno2TQ2WujI0HQd6s1Vw==", "path": "google.protobuf/3.30.2", "hashPath": "google.protobuf.3.30.2.nupkg.sha512"}, "Grpc.AspNetCore/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-B4wAbNtAuHNiHAMxLFWL74wUElzNOOboFnypalqpX76piCOGz/w5FpilbVVYGboI4Qgl4ZmZsvDZ1zLwHNsjnw==", "path": "grpc.aspnetcore/2.71.0", "hashPath": "grpc.aspnetcore.2.71.0.nupkg.sha512"}, "Grpc.AspNetCore.Server/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-kv+9YVB6MqDYWIcstXvWrT7Xc1si/sfINzzSxvQfjC3aei+92gXDUXCH/Q+TEvi4QSICRqu92BYcrXUBW7cuOw==", "path": "grpc.aspnetcore.server/2.71.0", "hashPath": "grpc.aspnetcore.server.2.71.0.nupkg.sha512"}, "Grpc.AspNetCore.Server.ClientFactory/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-AHvMxoC+esO1e/nOYBjxvn0WDHAfglcVBjtkBy6ohgnV+PzkF8UdkPHE02xnyPFaSokWGZKnWzjgd00x6EZpyQ==", "path": "grpc.aspnetcore.server.clientfactory/2.71.0", "hashPath": "grpc.aspnetcore.server.clientfactory.2.71.0.nupkg.sha512"}, "Grpc.Core.Api/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-QquqUC37yxsDzd1QaDRsH2+uuznWPTS8CVE2Yzwl3CvU4geTNkolQXoVN812M2IwT6zpv3jsZRc9ExJFNFslTg==", "path": "grpc.core.api/2.71.0", "hashPath": "grpc.core.api.2.71.0.nupkg.sha512"}, "Grpc.Net.Client/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-U1vr20r5ngoT9nlb7wejF28EKN+taMhJsV9XtK9MkiepTZwnKxxiarriiMfCHuDAfPUm9XUjFMn/RIuJ4YY61w==", "path": "grpc.net.client/2.71.0", "hashPath": "grpc.net.client.2.71.0.nupkg.sha512"}, "Grpc.Net.ClientFactory/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-8oPLwQLPo86fmcf9ghjCDyNsSWhtHc3CXa/AqwF8Su/pG7qAoeWWtbymsZhoNvCV9Zjzb6BDcIPKXLYt+O175g==", "path": "grpc.net.clientfactory/2.71.0", "hashPath": "grpc.net.clientfactory.2.71.0.nupkg.sha512"}, "Grpc.Net.Common/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-v0c8R97TwRYwNXlC8GyRXwYTCNufpDfUtj9la+wUrZFzVWkFJuNAltU+c0yI3zu0jl54k7en6u2WKgZgd57r2Q==", "path": "grpc.net.common/2.71.0", "hashPath": "grpc.net.common.2.71.0.nupkg.sha512"}, "Grpc.Tools/2.72.0": {"type": "package", "serviceable": true, "sha512": "sha512-BCiuQ03EYjLHCo9hqZmY5barsz5vvcz/+/ICt5wCbukaePHZmMPDGelKlkxWx3q+f5xOMNHa9zXQ2N6rQZ4B+w==", "path": "grpc.tools/2.72.0", "hashPath": "grpc.tools.2.72.0.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Json.More.Net/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-qtwsyAsL55y2vB2/sK4Pjg3ZyVzD5KKSpV3lOAMHlnjFfsjQ/86eHJfQT9aV1YysVXzF4+xyHOZbh7Iu3YQ7Lg==", "path": "json.more.net/2.1.0", "hashPath": "json.more.net.2.1.0.nupkg.sha512"}, "JsonPatch.Net/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GIcMMDtzfzVfIpQgey8w7dhzcw6jG5nD4DDAdQCTmHfblkCvN7mI8K03to8YyUhKMl4PTR6D6nLSvWmyOGFNTg==", "path": "jsonpatch.net/3.3.0", "hashPath": "jsonpatch.net.3.3.0.nupkg.sha512"}, "JsonPointer.Net/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-qe1F7Tr/p4mgwLPU9P60MbYkp+xnL2uCPnWXGgzfR/AZCunAZIC0RZ32dLGJJEhSuLEfm0YF/1R3u5C7mEVq+w==", "path": "jsonpointer.net/5.2.0", "hashPath": "jsonpointer.net.5.2.0.nupkg.sha512"}, "KubernetesClient/16.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-hH+YN18bpIRO/rq2CiMGDpLpc/KjSMlAn4EelFB4PgiswbSie4jANLAOou1Q39Kx7en2jO1Qp73y3SkjxGJIMg==", "path": "kubernetesclient/16.0.7", "hashPath": "kubernetesclient.16.0.7.nupkg.sha512"}, "MessagePack/2.5.192": {"type": "package", "serviceable": true, "sha512": "sha512-Jtle5MaFeIFkdXtxQeL9Tu2Y3HsAQGoSntOzrn6Br/jrl6c8QmG22GEioT5HBtZJR0zw0s46OnKU8ei2M3QifA==", "path": "messagepack/2.5.192", "hashPath": "messagepack.2.5.192.nupkg.sha512"}, "MessagePack.Annotations/2.5.192": {"type": "package", "serviceable": true, "sha512": "sha512-ja<PERSON><PERSON>w<PERSON>govWIZ8Zysdyf3b7b34/BrADw4v82GaEZymUhDd3ScMPrYd/cttekeDteJJPXseJxp04yTIcxiVUjTWg==", "path": "messagepack.annotations/2.5.192", "hashPath": "messagepack.annotations.2.5.192.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512"}, "Microsoft.CodeCoverage/17.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-yC7oSlnR54XO5kOuHlVOKtxomNNN1BWXX8lK1G2jaPXT9sUok7kCOoA4Pgs0qyFaCtMrNsprztYMeoEGqCm4uA==", "path": "microsoft.codecoverage/17.10.0", "hashPath": "microsoft.codecoverage.17.10.0.nupkg.sha512"}, "Microsoft.Extensions.AmbientMetadata.Application/9.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-GHgq9tfncuDSaPtwd1od9It3omPuq5PmHDkTWC91VRy75SWvvT7AX+zyDEurp1+oYgLEObQ6PICv+hZvht8dig==", "path": "microsoft.extensions.ambientmetadata.application/9.5.0", "hashPath": "microsoft.extensions.ambientmetadata.application.9.5.0.nupkg.sha512"}, "Microsoft.Extensions.Compliance.Abstractions/9.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-jK7bWPhu60GvcVSqMXOdV6ZLOP5rnwvmlqSD2E5fTkAXwoGYUV/5U3tQbvlZtOpeXTu509eg2VEb+l66d7dtSg==", "path": "microsoft.extensions.compliance.abstractions/9.5.0", "hashPath": "microsoft.extensions.compliance.abstractions.9.5.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-uYXLg2Gt8KUH5nT3u+TBpg9VrRcN5+2zPmIjqEHR4kOoBwsbtMDncEJw9HiLvZqGgIo2TR4oraibAoy5hXn2bQ==", "path": "microsoft.extensions.configuration/9.0.5", "hashPath": "microsoft.extensions.configuration.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-ew0G6gIznnyAkbIa67wXspkDFcVektjN3xaDAfBDIPbWph+rbuGaaohFxUSGw28ht7wdcWtTtElKnzfkcDDbOQ==", "path": "microsoft.extensions.configuration.abstractions/9.0.5", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-7pQ4Tkyofm8DFWFhqn9ZmG8qSAC2VitWleATj5qob9V9KtoxCVdwRtmiVl/ha3WAgjkEfW++JLWXox9MJwMgkg==", "path": "microsoft.extensions.configuration.binder/9.0.5", "hashPath": "microsoft.extensions.configuration.binder.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-TbM2HElARG7z1gxwakdppmOkm1SykPqDcu3EF97daEwSb/+TXnRrFfJtF+5FWWxcsNhbRrmLfS2WszYcab7u1A==", "path": "microsoft.extensions.configuration.commandline/9.0.4", "hashPath": "microsoft.extensions.configuration.commandline.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-2IGiG3FtVnD83IA6HYGuNei8dOw455C09yEhGl8bjcY6aGZgoC6yhYvDnozw8wlTowfoG9bxVrdTsr2ACZOYHg==", "path": "microsoft.extensions.configuration.environmentvariables/9.0.4", "hashPath": "microsoft.extensions.configuration.environmentvariables.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-UY864WQ3AS2Fkc8fYLombWnjrXwYt+BEHHps0hY4sxlgqaVW06AxbpgRZjfYf8PyRbplJqruzZDB/nSLT+7RLQ==", "path": "microsoft.extensions.configuration.fileextensions/9.0.4", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-vVXI70CgT/dmXV3MM+n/BR2rLXEoAyoK0hQT+8MrbCMuJBiLRxnTtSrksNiASWCwOtxo/Tyy7CO8AGthbsYxnw==", "path": "microsoft.extensions.configuration.json/9.0.4", "hashPath": "microsoft.extensions.configuration.json.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-zuvyC72gJkJyodyGowCuz3EQ1QvzNXJtKusuRzmjoHr17aeB3X0aSiKFB++HMHnQIWWlPOBf9YHTQfEqzbgl1g==", "path": "microsoft.extensions.configuration.usersecrets/9.0.4", "hashPath": "microsoft.extensions.configuration.usersecrets.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-N1Mn0T/tUBPoLL+Fzsp+VCEtneUhhxc1//Dx3BeuQ8AX+XrMlYCfnp2zgpEXnTCB7053CLdiqVWPZ7mEX6MPjg==", "path": "microsoft.extensions.dependencyinjection/9.0.5", "hashPath": "microsoft.extensions.dependencyinjection.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-cjnRtsEAzU73aN6W7vkWy8Phj5t3Xm78HSqgrbh/O4Q9SK/yN73wZVa21QQY6amSLQRQ/M8N+koGnY6PuvKQsw==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.5", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-JT0VpQl0yrkqdQB8IrN3IqI5frfH8BzuHSsHbNU0Y1qbG/F+gF6A7rBAZTR4NfxQHTwTrO7tnrrVixEmBQ/PyQ==", "path": "microsoft.extensions.dependencyinjection.autoactivation/9.5.0", "hashPath": "microsoft.extensions.dependencyinjection.autoactivation.9.5.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-fRiUjmhm9e4vMp6WEO9MgWNxVtWSr4Pcgh1W4DyJIr8bRANlZz9JU7uicf7ShzMspDxo/9Ejo9zJ6qQZY0IhVw==", "path": "microsoft.extensions.diagnostics/9.0.5", "hashPath": "microsoft.extensions.diagnostics.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-6YfTcULCYREMTqtk+s3UiszsFV2xN2FXtxdQpurmQJY9Cp/QGiM4MTKfJKUo7AzdLuzjOKKMWjQITmvtK7AsUg==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.5", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QKLs7fG05WqLyp5zOGENZ18XZzVJ2TCw7WTnsQcDwQqA34YOev2vBmIk4FwtPqPieGdqQhzcN032nZkKacIrdQ==", "path": "microsoft.extensions.diagnostics.exceptionsummarization/9.5.0", "hashPath": "microsoft.extensions.diagnostics.exceptionsummarization.9.5.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.HealthChecks/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-jW9lhWQzOOL5sBUCNtAiS6B7tGeLlxJVDjwNuQAQl6dDt9PAAxt3+T2F2jtcvi7KoujgzAdkKQKtGoRaAGlD9w==", "path": "microsoft.extensions.diagnostics.healthchecks/9.0.4", "hashPath": "microsoft.extensions.diagnostics.healthchecks.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-XM6WwNbDkVuGhDN89eKxA2Og2eMDXB0PVI7PEzl2R0MbFjYUlfTh7D7vBPEWUVCf2zPDAFiwcMlnVzi6Umq5mg==", "path": "microsoft.extensions.diagnostics.healthchecks.abstractions/9.0.4", "hashPath": "microsoft.extensions.diagnostics.healthchecks.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-LLm+e8lvD+jOI+blHRSxPqywPaohOTNcVzQv548R1UpkEiNB2D+zf3RrqxBdB1LDPicRMTnfiaKJovxF8oX1bQ==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.5", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-qkQ9V7KFZdTWNThT7ke7E/Jad38s46atSs3QUYZB8f3thBTrcrousdY4Y/tyCtcH5YjsPSiByjuN+L8W/ThMQg==", "path": "microsoft.extensions.fileproviders.physical/9.0.4", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-05Lh2ItSk4mzTdDWATW9nEcSybwprN8Tz42Fs5B+jwdXUpauktdAQUI1Am4sUQi2C63E5hvQp8gXvfwfg9mQGQ==", "path": "microsoft.extensions.filesystemglobbing/9.0.4", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Hosting/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-1rZwLE+tTUIyZRUzmlk/DQj+v+Eqox+rjb+X7Fi+cYTbQfIZPYwpf1pVybsV3oje8+Pe4GaNukpBVUlPYeQdeQ==", "path": "microsoft.extensions.hosting/9.0.4", "hashPath": "microsoft.extensions.hosting.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-3GA/dxqkP6yFe18qYRgtKYuN2onC8NfhlpNN21jptkVKk7olqBTkdT49oL0pSEz2SptRsux7LocCU7+alGnEag==", "path": "microsoft.extensions.hosting.abstractions/9.0.5", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Http/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-6vbo3XjyEc+w/kv/Dkfv9NA7iSdIdX5dlU9Shk3wJJ0fiZpCVzVW5FJtNoIePX5hS0ENNpHPClq/qtq06yM4FQ==", "path": "microsoft.extensions.http/9.0.5", "hashPath": "microsoft.extensions.http.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Http.Diagnostics/9.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-K5VI2QA3Y2s675IyyhA34xKkUK0XZKgXU0ddXOkuCuHiEWLKs/McNVGP1Qw5B5UEGxrei7aj20Ssm+9JJ6yTlg==", "path": "microsoft.extensions.http.diagnostics/9.5.0", "hashPath": "microsoft.extensions.http.diagnostics.9.5.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Resilience/9.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-6+D5dTm6t+Rg93ODzQ/obWuUxl+YPEm6bU4lz6dA2lxPjJmqbsUKP3l2bXlMcahj3EFj9n4gpRD7fzF46q+ecQ==", "path": "microsoft.extensions.http.resilience/9.5.0", "hashPath": "microsoft.extensions.http.resilience.9.5.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-rQU61lrgvpE/UgcAd4E56HPxUIkX/VUQCxWmwDTLLVeuwRDYTL0q/FLGfAW17cGTKyCh7ywYAEnY3sTEvURsfg==", "path": "microsoft.extensions.logging/9.0.5", "hashPath": "microsoft.extensions.logging.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-pP1PADCrIxMYJXxFmTVbAgEU7GVpjK5i0/tyfU9DiE0oXQy3JWQaOVgCkrCiePLgS8b5sghM3Fau3EeHiVWbCg==", "path": "microsoft.extensions.logging.abstractions/9.0.5", "hashPath": "microsoft.extensions.logging.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-WgYTJ1/dxdzqaYYMrgC6cZXJVmaoxUmWgsvR9Kg5ZARpy0LMw7fZIZMIiVuaxhItwwFIW0ruhAN+Er2/oVZgmQ==", "path": "microsoft.extensions.logging.configuration/9.0.5", "hashPath": "microsoft.extensions.logging.configuration.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-cI0lQe0js65INCTCtAgnlVJWKgzgoRHVAW1B1zwCbmcliO4IZoTf92f1SYbLeLk7FzMJ/GlCvjLvJegJ6kltmQ==", "path": "microsoft.extensions.logging.console/9.0.4", "hashPath": "microsoft.extensions.logging.console.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-D1jy+jy+huUUxnkZ0H480RZK8vqKn8NsQxYpMpPL/ALPPh1WATVLcr/uXI3RUBB45wMW5265O+hk9x3jnnXFuA==", "path": "microsoft.extensions.logging.debug/9.0.4", "hashPath": "microsoft.extensions.logging.debug.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-bApxdklf7QTsONOLR5ow6SdDFXR5ncHvumSEg2+QnCvxvkzc2z5kNn7yQCyupRLRN4jKbnlTkVX8x9qLlwL6Qg==", "path": "microsoft.extensions.logging.eventlog/9.0.4", "hashPath": "microsoft.extensions.logging.eventlog.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-R600zTxVJNw2IeAEOvdOJGNA1lHr1m3vo460hSF5G1DjwP0FNpyeH4lpLDMuf34diKwB1LTt5hBw1iF1/iuwsQ==", "path": "microsoft.extensions.logging.eventsource/9.0.4", "hashPath": "microsoft.extensions.logging.eventsource.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-C0VDKwSwNfc3YCLuno6Dip0un9LFmvuSvhpCC4ckpz6nrOmiM5JSJspQiY1dGCDRXJKFeZxa2hDpCLRL8WiBhw==", "path": "microsoft.extensions.objectpool/9.0.5", "hashPath": "microsoft.extensions.objectpool.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-vPdJQU8YLOUSSK8NL0RmwcXJr2E0w8xH559PGQl4JYsglgilZr9LZnqV2zdgk+XR05+kuvhBEZKoDVd46o7NqA==", "path": "microsoft.extensions.options/9.0.5", "hashPath": "microsoft.extensions.options.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-CJbAVdovKPFh2FoKxesu20odRVSbL/vtvzzObnG+5u38sOfzRS2Ncy25id0TjYUGQzMhNnJUHgTUzTMDl/3c9g==", "path": "microsoft.extensions.options.configurationextensions/9.0.5", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-b4OAv1qE1C9aM+ShWJu3rlo/WjDwa/I30aIPXqDWSKXTtKl1Wwh6BZn+glH5HndGVVn3C6ZAPQj5nv7/7HJNBQ==", "path": "microsoft.extensions.primitives/9.0.5", "hashPath": "microsoft.extensions.primitives.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Resilience/9.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-KoL2rlNW/l+CWfiIFb8ZjBaqgY5DSnla5ZIxBvt5BdkGZ+xoe7ZMEw62FeQ+yot053VEPbPpZ/iTnX3zZ6kB3Q==", "path": "microsoft.extensions.resilience/9.5.0", "hashPath": "microsoft.extensions.resilience.9.5.0.nupkg.sha512"}, "Microsoft.Extensions.Telemetry/9.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-oaLcdlYgsEl6YAUdwrBHFakGgkHxifOTZQXu6T57OvRm0DByJhn2YyeijUOM2iDIDUJObZhG5A9qL/JNehZydw==", "path": "microsoft.extensions.telemetry/9.5.0", "hashPath": "microsoft.extensions.telemetry.9.5.0.nupkg.sha512"}, "Microsoft.Extensions.Telemetry.Abstractions/9.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-vYQAUcMkW06G973ocSzJT5WFWSN7J5l3yZTQF99nUhWP/pJ1SjYBxXXCk/7jqYnBjFJgsmdrP+JxqJio/EvyQQ==", "path": "microsoft.extensions.telemetry.abstractions/9.5.0", "hashPath": "microsoft.extensions.telemetry.abstractions.9.5.0.nupkg.sha512"}, "Microsoft.NET.StringTools/17.6.3": {"type": "package", "serviceable": true, "sha512": "sha512-N0ZIanl1QCgvUumEL1laasU0a7sOE5ZwLZVTn0pAePnfhq8P7SvTjF8Axq+CnavuQkmdQpGNXQ1efZtu5kDFbA==", "path": "microsoft.net.stringtools/17.6.3", "hashPath": "microsoft.net.stringtools.17.6.3.nupkg.sha512"}, "Microsoft.NET.Test.Sdk/17.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-0/2HeACkaHEYU3wc83YlcD2Fi4LMtECJjqrtvw0lPi9DCEa35zSPt1j4fuvM8NagjDqJuh1Ja35WcRtn1Um6/A==", "path": "microsoft.net.test.sdk/17.10.0", "hashPath": "microsoft.net.test.sdk.17.10.0.nupkg.sha512"}, "Microsoft.TestPlatform.ObjectModel/17.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-KkwhjQevuDj0aBRoPLY6OLAhGqbPUEBuKLbaCs0kUVw29qiOYncdORd4mLVJbn9vGZ7/iFGQ/+AoJl0Tu5Umdg==", "path": "microsoft.testplatform.objectmodel/17.10.0", "hashPath": "microsoft.testplatform.objectmodel.17.10.0.nupkg.sha512"}, "Microsoft.TestPlatform.TestHost/17.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-LWpMdfqhHvcUkeMCvNYJO8QlPLlYz9XPPb+ZbaXIKhdmjAV0wqTSrTiW5FLaf7RRZT50AQADDOYMOe0HxDxNgA==", "path": "microsoft.testplatform.testhost/17.10.0", "hashPath": "microsoft.testplatform.testhost.17.10.0.nupkg.sha512"}, "Microsoft.VisualStudio.Threading.Only/17.13.61": {"type": "package", "serviceable": true, "sha512": "sha512-vl5a2URJYCO5m+aZZtNlAXAMz28e2pUotRuoHD7RnCWOCeoyd8hWp5ZBaLNYq4iEj2oeJx5ZxiSboAjVmB20Qg==", "path": "microsoft.visualstudio.threading.only/17.13.61", "hashPath": "microsoft.visualstudio.threading.only.17.13.61.nupkg.sha512"}, "Microsoft.VisualStudio.Validation/17.8.8": {"type": "package", "serviceable": true, "sha512": "sha512-rWXThIpyQd4YIXghNkiv2+VLvzS+MCMKVRDR0GAMlflsdo+YcAN2g2r5U1Ah98OFjQMRexTFtXQQ2LkajxZi3g==", "path": "microsoft.visualstudio.validation/17.8.8", "hashPath": "microsoft.visualstudio.validation.17.8.8.nupkg.sha512"}, "Nerdbank.Streams/2.11.90": {"type": "package", "serviceable": true, "sha512": "sha512-7jrOfU6b/PVBccqzNLfw9u84WWzkSpvWLb2mZxvwdQkOx/V9FXWkmnp/rjOnBFDOhrO/ev4+gQ5QS13FkgNSBA==", "path": "nerdbank.streams/2.11.90", "hashPath": "nerdbank.streams.2.11.90.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Polly.Core/8.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-1MJKdxv4zwDmiWvYvVN24DsrWUfgQ4F83voH8bhbtLMdPuGy8CfTUzsgQhvyrl1a7hrM6f/ydwLVdVUI0xooUw==", "path": "polly.core/8.5.2", "hashPath": "polly.core.8.5.2.nupkg.sha512"}, "Polly.Extensions/8.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-NcUEkeMB7LMsLBXzY9PuuKP7ylXngUEuhmaWxCrlXqYLbVQQzG1ulAnaEQ2LM1zlSoxRftAjCuhPIH40qeWAlg==", "path": "polly.extensions/8.5.2", "hashPath": "polly.extensions.8.5.2.nupkg.sha512"}, "Polly.RateLimiting/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-ehTImQ/eUyO07VYW2WvwSmU9rRH200SKJ/3jku9rOkyWE0A2JxNFmAVms8dSn49QLSjmjFRRSgfNyOgr/2PSmA==", "path": "polly.ratelimiting/8.4.2", "hashPath": "polly.ratelimiting.8.4.2.nupkg.sha512"}, "StreamJsonRpc/2.21.69": {"type": "package", "serviceable": true, "sha512": "sha512-WbTpn/PIo+HpFYnsOCiOOe0kHUE2N1eiVRi7MO70DFBTMG3pAOfrgHtwUpOJ37dfDETq/9P9WNIbHom4ABZfrA==", "path": "streamjsonrpc/2.21.69", "hashPath": "streamjsonrpc.2.21.69.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-getRQEXD8idlpb1KW56XuxImMy0FKp2WJPDf3Qr0kI/QKxxJSftqfDFVo0DZ3HCJRLU73qHSruv5q2l5O47jQQ==", "path": "system.diagnostics.eventlog/9.0.4", "hashPath": "system.diagnostics.eventlog.9.0.4.nupkg.sha512"}, "System.IO.Hashing/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-WogPvgAFqQORFD8Iyha6RZ+/1QB3dsWRWxbwi8/HHVgiGQ8z0oMWpwe8Kk3Ti+Roe+P6a3sBg+WwBfEsyziZKg==", "path": "system.io.hashing/9.0.4", "hashPath": "system.io.hashing.9.0.4.nupkg.sha512"}, "System.IO.Pipelines/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-5WXo+3MGcnYn54+1ojf+kRzKq1Q6sDUnovujNJ2ky1nl1/kP3+PMil9LPbFvZ2mkhvAGmQcY07G2sfHat/v0Fw==", "path": "system.io.pipelines/9.0.5", "hashPath": "system.io.pipelines.9.0.5.nupkg.sha512"}, "System.Threading.RateLimiting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7mu9v0QDv66ar3DpGSZHg9NuNcxDaaAcnMULuZlaTpP9+hwXhrxNGsF5GmLkSHxFdb5bBc1TzeujsRgTrPWi+Q==", "path": "system.threading.ratelimiting/8.0.0", "hashPath": "system.threading.ratelimiting.8.0.0.nupkg.sha512"}, "xunit/2.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-PtU3rZ0ThdmdJqTbK7GkgFf6iBaCR6Q0uvJHznID+XEYk2v6O/b7sRxqnbi3B2gRDXxjTqMkVNayzwsqsFUxRw==", "path": "xunit/2.9.0", "hashPath": "xunit.2.9.0.nupkg.sha512"}, "xunit.abstractions/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-pot1I4YOxlWjIb5jmwvvQNbTrZ3lJQ+jUGkGjWE3hEFM0l5gOnBWS+H3qsex68s5cO52g+44vpGzhAt+42vwKg==", "path": "xunit.abstractions/2.0.3", "hashPath": "xunit.abstractions.2.0.3.nupkg.sha512"}, "xunit.analyzers/1.15.0": {"type": "package", "serviceable": true, "sha512": "sha512-s+M8K/Rtlgr6CmD7AYQKrNTvT5sh0l0ZKDoZ3Z/ExhlIwfV9mGAMR4f7KqIB7SSK7ZOhqDTgTUMYPmKfmvWUWQ==", "path": "xunit.analyzers/1.15.0", "hashPath": "xunit.analyzers.1.15.0.nupkg.sha512"}, "xunit.assert/2.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z/1pyia//860wEYTKn6Q5dmgikJdRjgE4t5AoxJkK8oTmidzPLEPG574kmm7LFkMLbH6Frwmgb750kcyR+hwoA==", "path": "xunit.assert/2.9.0", "hashPath": "xunit.assert.2.9.0.nupkg.sha512"}, "xunit.core/2.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-uRaop9tZsZMCaUS4AfbSPGYHtvywWnm8XXFNUqII7ShWyDBgdchY6gyDNgO4AK1Lv/1NNW61Zq63CsDV6oH6Jg==", "path": "xunit.core/2.9.0", "hashPath": "xunit.core.2.9.0.nupkg.sha512"}, "xunit.extensibility.core/2.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-zjDEUSxsr6UNij4gIwCgMqQox+oLDPRZ+mubwWLci+SssPBFQD1xeRR4SvgBuXqbE0QXCJ/STVTp+lxiB5NLVA==", "path": "xunit.extensibility.core/2.9.0", "hashPath": "xunit.extensibility.core.2.9.0.nupkg.sha512"}, "xunit.extensibility.execution/2.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-5ZTQZvmPLlBw6QzCOwM0KnMsZw6eGjbmC176QHZlcbQoMhGIeGcYzYwn5w9yXxf+4phtplMuVqTpTbFDQh2bqQ==", "path": "xunit.extensibility.execution/2.9.0", "hashPath": "xunit.extensibility.execution.2.9.0.nupkg.sha512"}, "xunit.runner.visualstudio/2.8.2": {"type": "package", "serviceable": true, "sha512": "sha512-vm1tbfXhFmjFMUmS4M0J0ASXz3/U5XvXBa6DOQUL3fEz4Vt6YPhv+ESCarx6M6D+9kJkJYZKCNvJMas1+nVfmQ==", "path": "xunit.runner.visualstudio/2.8.2", "hashPath": "xunit.runner.visualstudio.2.8.2.nupkg.sha512"}, "YamlDotNet/16.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SgMOdxbz8X65z8hraIs6hOEdnkH6hESTAIUa7viEngHOYaH+6q5XJmwr1+yb9vJpNQ19hCQY69xbFsLtXpobQA==", "path": "yamldotnet/16.3.0", "hashPath": "yamldotnet.16.3.0.nupkg.sha512"}, "TaskFlow.AppHost/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}