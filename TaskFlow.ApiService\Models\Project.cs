﻿// TaskFlow.ApiService/Models/Project.cs
using System.ComponentModel.DataAnnotations;

namespace TaskFlow.ApiService.Models;

public class Project
{
    public int Id { get; set; }

    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    [StringLength(500)]
    public string? Description { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public List<TaskItem> Tasks { get; set; } = new();
    public List<Sprint> Sprints { get; set; } = new();
}
