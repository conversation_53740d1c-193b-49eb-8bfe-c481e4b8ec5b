{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["JDDvvUwh4m/BuwRp7DBe9lGdZZxsqNbTcttVRCW7erI=", "BhHKpNP7999B2sZpPI0Db2uA+q+aPDpFHc85V47xdF0=", "+OKe86qNEz0WAd/EtkBiXyg9CA47W4Ro5y4fI62hkxk=", "s26umhoorG86D205D48XGpHKpD93NNXRUav1hfyyCX0=", "pkaBfoVqo+vYFEqyNyIknlnH9x3HbGmSTwBn3XrrtHE=", "1FxiS1MHRA3nUVJHoUH5ZC7L0MU70vgnVMPaTolADFA=", "u1a5LNgr0JY2POuOzvt3USL4c7H5EtxlvBiFlWjMAEg=", "hBzZ2/lvOhnhu4DlrFDonpIV2YCFW3CHk2XaYGB91QQ=", "4DHhHC9Wc/DWtsgqN70m0rSwX8Rrb304kYerH43Nb5U=", "FhWHLeqgcpomeTWiUDeSQwhRTuEPuKyDpmvWeQ3jzG8=", "u9gQKsnytl6cE1y7PofPZESUhDhwajiYuF7ejQ2NQbw=", "YmgMOS/ogHlIop5vai4oLmX0p52Byxw0kBYfpE5Jlnw=", "cgDPsh5ExKN9xZVE7HsjJTJxzlDWF3WpLYEH5ZR26nY=", "kd1hZvvEMmgg0H/gbLfghTZ2o4uYiUV8qkpVL4kMm5s=", "r1G2AvYOrAQnA5Bsd0mVY+IO+rmv6Zw+dxfw+C7spV4=", "5BpbUZh/wrGc4xE/FqYZW06ScdYPRPZdwFUM2xEEBSo=", "3Gg1+CldqUWPFUkYsrPK/rcZzFMGKwU66pXDk+Ldkpg=", "NKaAN8soKsOW3wQMsoBR3BN4fpBIGQz5FYhX3OYdCnQ=", "C/8GYdTLSBBNJbBvKH1FLxUYszKHv7N3NPxAEE5jeQI=", "wpv74gYq74ghGH2Q04UNAaMupOkxwLRhCAmvekxd+Yg=", "4QgS3CM22wqBDp7wpeS+0/8KxysIo9eXEjXukVvsKEw=", "Clj0FiggmlySM3gZCxIG5DTNCa0dJq3zdX6lB3RP5+M=", "igTq0LnYVyJkAlRJYmCbS1FmgNDJU3Xqn/NuiF0V4TE=", "JzcSr/t82LgCyKWpnL1JxDy6DcaHqeosC3OqEMXevmU=", "LdWRVa6MENxX7piVuTshSXc5XKu0dIws5kYi4pA7pkQ=", "lnGMzyv1zgO1OK8K2THbnNA7rUyJm0HcGPaTCcKsfJc=", "XGWWGxw6IPtXDyJ/2L95wfkODDMt2xIxBrErL5T96KY=", "ihc9+ASd87lJxpiG8z3H2ycYJg1wBUi3csaXOtI4ZU0=", "IQlYYFm2y/c0Iia9gGbXeGvZbrfRkMampv6A1HedR4A=", "ya89t/h7Vrkmdtfh1i8I6VzSLf5qn5yJVg1C418dfKw=", "+zevNvrgH2/VQoS5+TTGDOlcPKbqXeYduFFI8wIxHMQ=", "al+uzfbSl1k0UnuIicwykMJmjVXkgqmGtO4rLJs+H2g=", "pTV0Jl3+1xIEfeAXHOzKTGUwamSZ3BN/19zEmyXVZoo=", "253RYkSAZOZPAd+UwD8XqBik1P8BJTyagu7qlwenEMw=", "XUymjk+oEHLfSJFbbS8EB1JPKN4zBxK8p13kVqTI5PU=", "NMxdE4KQJnJ4YVvInfB5zQH46c4NkM4CTVbT5WPZg6k=", "t/12TbM1Q9CI6xVNqh9NYmvhQh1giZKIcpyBZLkH23Y=", "QgVV04ZwcGeTVNuUZjMPInc6GB/7U76hGADMHY1R8Kc=", "G9VFfxLTCtOu7EZoDdQRcTc9PbxF81SccpTc7JbTRQE=", "lrwOe+A2egrHZtmFbmDfNCYufSLHlsib4IzYffsr1gw=", "Txxjuoy93NxTkp/bi0sOjM5hfON+tGSjeWA944Vqncc=", "pPxxtXAKaeRCHP8wr72Yp2u9iXD5KqAlzTjJn40L2eI=", "r1oAMOq5VM6LT+yCzoM9+YR5y6D/1G9p9/+ffNHsVoI=", "vmRUCC8sitrA/PzwPIF0+IgVL3DH3PwxW4y6RJM9Aq8=", "cWs4BSz3y/YLLGnsvc0Ri9RRLiqwoZ6cQQNjwWwrA9g=", "69r5ngAwzOrxt02Vz6F/rjogBYHYJ3l+xt1DYVJLBDI=", "OJ8xNHt4stcM3ZyRuTpjPPr0/lr4v74AE4ujL9G2VB8="], "CachedAssets": {"OJ8xNHt4stcM3ZyRuTpjPPr0/lr4v74AE4ujL9G2VB8=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\t9yymp5818-khv3du26ro.gz", "SourceId": "TaskFlow.Web", "SourceType": "Computed", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "TaskFlow.Web#[.{fingerprint=khv3du26ro}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\TaskFlow.Web.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qio0wa7dq4", "Integrity": "3kukm/KdHr/IeuPxj5KccsO2sAPAh6H7nS1H8KkBXag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\TaskFlow.Web.bundle.scp.css", "FileLength": 1726, "LastWriteTime": "2025-05-24T10:56:12.5791493+00:00"}, "69r5ngAwzOrxt02Vz6F/rjogBYHYJ3l+xt1DYVJLBDI=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\o0dcj0py4z-khv3du26ro.gz", "SourceId": "TaskFlow.Web", "SourceType": "Computed", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "TaskFlow.Web#[.{fingerprint=khv3du26ro}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\TaskFlow.Web.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qio0wa7dq4", "Integrity": "3kukm/KdHr/IeuPxj5KccsO2sAPAh6H7nS1H8KkBXag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\TaskFlow.Web.styles.css", "FileLength": 1726, "LastWriteTime": "2025-05-24T10:56:12.5771454+00:00"}, "cWs4BSz3y/YLLGnsvc0Ri9RRLiqwoZ6cQQNjwWwrA9g=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\1sqahdjw2w-knl5pustf3.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=knl5pustf3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4fvb7x223m", "Integrity": "0J7w1dP0MX5/Lq9zF4V4K7gVAABXENQz7lVXjj7Wevk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55527, "LastWriteTime": "2025-05-24T10:56:12.5831507+00:00"}, "vmRUCC8sitrA/PzwPIF0+IgVL3DH3PwxW4y6RJM9Aq8=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\6f8bul21g6-iug4zs40ca.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=iug4zs40ca}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "juqf57w4eu", "Integrity": "q9bmuLyWvdwq3sG1SZ2Nb1L/TX4YPe/lagO52vSSmfk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 26287, "LastWriteTime": "2025-05-24T10:56:12.5866594+00:00"}, "r1oAMOq5VM6LT+yCzoM9+YR5y6D/1G9p9/+ffNHsVoI=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\lqmvts9dba-xxsbxg4q4i.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=xxsbxg4q4i}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nbafmbyl0j", "Integrity": "AyQbaPEKQmJNrT3QodbzX3og/IeV4b1vJDIDu7zcO7k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64493, "LastWriteTime": "2025-05-24T10:56:12.5821503+00:00"}, "pPxxtXAKaeRCHP8wr72Yp2u9iXD5KqAlzTjJn40L2eI=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\oac7z3pbsy-1jo1kezdci.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=1jo1kezdci}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l0sonv57g4", "Integrity": "K/nHzlmp4BDVGVPLGmX+biFDA65Yck6I9ghtrnvvxIM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 38735, "LastWriteTime": "2025-05-24T10:56:12.5984304+00:00"}, "Txxjuoy93NxTkp/bi0sOjM5hfON+tGSjeWA944Vqncc=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\sbc2oexvxn-u3orf5n9fb.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=u3orf5n9fb}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v5bp33e3ma", "Integrity": "PYy136oKKGrOXCRjTCx1whlZEA/E570vlrHbZnVYRfg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56388, "LastWriteTime": "2025-05-24T10:56:12.5916745+00:00"}, "lrwOe+A2egrHZtmFbmDfNCYufSLHlsib4IzYffsr1gw=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\qbt2gjo1ua-l53j0jwsua.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=l53j0jwsua}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u66s2s6zb1", "Integrity": "VmC9C7DsdC9Uc6NzAI+V2IN2CgW7t3Q/Ch1PbuX3Lx8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 28185, "LastWriteTime": "2025-05-24T10:56:12.5706302+00:00"}, "G9VFfxLTCtOu7EZoDdQRcTc9PbxF81SccpTc7JbTRQE=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\w8qngq9ie1-sybl3t77fa.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=sybl3t77fa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ip0pxmmugs", "Integrity": "1ohABc+tAKQtdKLydR8wiyGBqq1Zx2Wblgcc1a7rdkE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64237, "LastWriteTime": "2025-05-24T10:56:12.5866594+00:00"}, "QgVV04ZwcGeTVNuUZjMPInc6GB/7U76hGADMHY1R8Kc=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\zgyeaunyzm-xy033osspr.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=xy033osspr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tawlds0yav", "Integrity": "O8xubomQsbyaR9sXUShcb/7M93vwQ6E3KOkn/Uygo94=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 38028, "LastWriteTime": "2025-05-24T10:56:12.567622+00:00"}, "t/12TbM1Q9CI6xVNqh9NYmvhQh1giZKIcpyBZLkH23Y=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\vq2fdwhkxj-07r8e49zdp.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=07r8e49zdp}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fdz6gps3vo", "Integrity": "bXN3eVcTWo/7qlzCSB2GJ7cc4T8FNFg7FyUrS1IQyHI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86521, "LastWriteTime": "2025-05-24T10:56:12.5601093+00:00"}, "NMxdE4KQJnJ4YVvInfB5zQH46c4NkM4CTVbT5WPZg6k=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\cq5p1v8hv9-sebe1i1n8x.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=sebe1i1n8x}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mq5nrout7u", "Integrity": "zvfz0svKGBguAFWnOdskdgJyC+QoppLYTGHWd2CcEXc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 32948, "LastWriteTime": "2025-05-24T10:56:12.5876684+00:00"}, "XUymjk+oEHLfSJFbbS8EB1JPKN4zBxK8p13kVqTI5PU=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\nr8pparx2w-m8m04ow07k.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=m8m04ow07k}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tjhz08iwws", "Integrity": "mYPFV7neh848ZHnjFSndiOGmoOJ6qM/LXlieNp2qbps=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92279, "LastWriteTime": "2025-05-24T10:56:12.5811514+00:00"}, "253RYkSAZOZPAd+UwD8XqBik1P8BJTyagu7qlwenEMw=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\1ctw0o17fe-aak5eirmym.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=aak5eirmym}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nfs2v6gnbz", "Integrity": "cxPsMeyY7YQ+q+5bgzhtzLxQVrRUKsJsEOZ8Zi5hjnM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 54528, "LastWriteTime": "2025-05-24T10:56:12.5511664+00:00"}, "pTV0Jl3+1xIEfeAXHOzKTGUwamSZ3BN/19zEmyXVZoo=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\zt8myftf3o-ft3s53vfgj.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uom9ela1zq", "Integrity": "4gJzy0olXjj0z70VKojvdJzPHOLLNp9HrIQJNzkwxWE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91866, "LastWriteTime": "2025-05-24T10:56:12.6043547+00:00"}, "al+uzfbSl1k0UnuIicwykMJmjVXkgqmGtO4rLJs+H2g=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\11y2nf4j36-c63t5i9ira.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=c63t5i9ira}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a66bztmsz2", "Integrity": "AuvSI7nlwZgOHR4lTv48WmlYy8v4vpvtnbAtTSHGmTQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 31142, "LastWriteTime": "2025-05-24T10:56:12.567622+00:00"}, "+zevNvrgH2/VQoS5+TTGDOlcPKbqXeYduFFI8wIxHMQ=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\iyscw6kjtk-hrwsygsryq.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2knqut3gi", "Integrity": "YedRlLwXS/p1OuS/d0RjDFf5lQsoglvovzJIcXC8mWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114877, "LastWriteTime": "2025-05-24T10:56:12.5651102+00:00"}, "ya89t/h7Vrkmdtfh1i8I6VzSLf5qn5yJVg1C418dfKw=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\c1xriqn00w-ynyaa8k90p.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=ynyaa8k90p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ws8e397h8g", "Integrity": "EHv6Fxyfhm4sQaIznaio1clpsqF/v/3Le3ecZHQESCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33667, "LastWriteTime": "2025-05-24T10:56:12.5866594+00:00"}, "IQlYYFm2y/c0Iia9gGbXeGvZbrfRkMampv6A1HedR4A=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\6e4f3aw9u8-v0zj4ognzu.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "77t5enldb7", "Integrity": "Ez3ceWLr2D4WzWEtd0AgNaoRd/oY3XS1P5pFtgjHfAs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91960, "LastWriteTime": "2025-05-24T10:56:12.5746287+00:00"}, "ihc9+ASd87lJxpiG8z3H2ycYJg1wBUi3csaXOtI4ZU0=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\xv7sgvmssy-43atpzeawx.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=43atpzeawx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ny7oqyylde", "Integrity": "sT/qLvBmXOeeXFjXr+qriR/tEhjqoanwO9cF0pJ6+Yk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 31123, "LastWriteTime": "2025-05-24T10:56:12.5442839+00:00"}, "XGWWGxw6IPtXDyJ/2L95wfkODDMt2xIxBrErL5T96KY=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ktuithl01y-pj5nd1wqec.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "glev5uv9kg", "Integrity": "pOh8WLH4j3VtIjwkYu8FxMwfZXN1u9bs0TmE+R/LeKU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 114902, "LastWriteTime": "2025-05-24T10:56:12.6063564+00:00"}, "lnGMzyv1zgO1OK8K2THbnNA7rUyJm0HcGPaTCcKsfJc=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\c6qmdw4ri0-zub09dkrxp.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=zub09dkrxp}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ndo96zskmb", "Integrity": "q9WZtmMnJ+oJNgmN0eeT5IvKsKV1/YleuDnjGVjv4ps=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33770, "LastWriteTime": "2025-05-24T10:56:12.5841522+00:00"}, "LdWRVa6MENxX7piVuTshSXc5XKu0dIws5kYi4pA7pkQ=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\jsy329rgjt-nvvlpmu67g.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0gru265j2n", "Integrity": "cBDhSnwBZjhD+2ai7wNkh+CWcMrzxwfOe6IVNDuakD4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24456, "LastWriteTime": "2025-05-24T10:56:12.543286+00:00"}, "JzcSr/t82LgCyKWpnL1JxDy6DcaHqeosC3OqEMXevmU=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\bkl21n1ixh-keugtjm085.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=keugtjm085}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b271r4kg0j", "Integrity": "A6WMzqJSc0ku4mP+TCXA3zO5Xwz4hHan7U/eu325Y4A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11132, "LastWriteTime": "2025-05-24T10:56:12.5736299+00:00"}, "igTq0LnYVyJkAlRJYmCbS1FmgNDJU3Xqn/NuiF0V4TE=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\3uw1dz2yv7-j5mq2jizvt.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pnmw3mh9ht", "Integrity": "tYXT5KEqImURIzC3Qanl3vg3XPSckCiDY2iFihvBz4w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44237, "LastWriteTime": "2025-05-24T10:56:12.567622+00:00"}, "Clj0FiggmlySM3gZCxIG5DTNCa0dJq3zdX6lB3RP5+M=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\pybm8mc09z-d4r6k3f320.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=d4r6k3f320}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a9e8v2fr2b", "Integrity": "0o55tA+sg7mY2XRLNBCJpRjgbMaoOXFrmEpYn5eaikg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 12194, "LastWriteTime": "2025-05-24T10:56:12.5402828+00:00"}, "4QgS3CM22wqBDp7wpeS+0/8KxysIo9eXEjXukVvsKEw=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\1ork37jani-c2oey78nd0.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oiolxl3gck", "Integrity": "oCsmmiBhwTjmHD8BM0Q5rOz0kfUetk7G5Eydczr8npA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24487, "LastWriteTime": "2025-05-24T10:56:12.5666133+00:00"}, "wpv74gYq74ghGH2Q04UNAaMupOkxwLRhCAmvekxd+Yg=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ovchpumm0y-wl58j5mj3v.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=wl58j5mj3v}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2dzz7zk1je", "Integrity": "Og/4AkcNfLOxVj/DmATc4F18q6RjYgz/OrjtVYYkZTQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11153, "LastWriteTime": "2025-05-24T10:56:12.5581013+00:00"}, "C/8GYdTLSBBNJbBvKH1FLxUYszKHv7N3NPxAEE5jeQI=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\v6x955lyjj-r4e9w2rdcm.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "21g01g2f66", "Integrity": "JhJG+P11KpccX3eLLCfr9IvrrIGljBXVVu8i06COWfo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44266, "LastWriteTime": "2025-05-24T10:56:12.5545842+00:00"}, "NKaAN8soKsOW3wQMsoBR3BN4fpBIGQz5FYhX3OYdCnQ=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\nuxbc26o8n-gye83jo8yx.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=gye83jo8yx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g3pw7iimyz", "Integrity": "DNbwelsqgYZDvJGAT2jwEYUmCQ08fVNGjkxqe572kmk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 12244, "LastWriteTime": "2025-05-24T10:56:12.5581013+00:00"}, "3Gg1+CldqUWPFUkYsrPK/rcZzFMGKwU66pXDk+Ldkpg=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\krrntovrdp-jd9uben2k1.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ldvkm706vq", "Integrity": "J1KmNvA0YsTrxABdpOt20g2Pc9ye2G9nKQ+doaPxfWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15028, "LastWriteTime": "2025-05-24T10:56:12.5535853+00:00"}, "5BpbUZh/wrGc4xE/FqYZW06ScdYPRPZdwFUM2xEEBSo=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\fcr8eb906x-q9ht133ko3.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=q9ht133ko3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d645a0veuj", "Integrity": "ZV74uw9avCkAGQbxeK2wfps8314gun6xEJG4X6KsOXY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3235, "LastWriteTime": "2025-05-24T10:56:12.5412834+00:00"}, "r1G2AvYOrAQnA5Bsd0mVY+IO+rmv6Zw+dxfw+C7spV4=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\2cc7g8dnkc-ee0r1s7dh0.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kqk14ew2nl", "Integrity": "PBumXrpJefIUX366kZ07GYowOsT9YFX4xEFYFHevQag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25726, "LastWriteTime": "2025-05-24T10:56:12.5896754+00:00"}, "kd1hZvvEMmgg0H/gbLfghTZ2o4uYiUV8qkpVL4kMm5s=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\8a0aj51wdw-rxsg74s51o.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rxsg74s51o}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fjx614p1f2", "Integrity": "tQ/ZHQ02xDDr7P2Fg/W0cSQUbq1b8IQ7Xs+98LoDdrI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3395, "LastWriteTime": "2025-05-24T10:56:12.5422831+00:00"}, "cgDPsh5ExKN9xZVE7HsjJTJxzlDWF3WpLYEH5ZR26nY=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ys21i01zgw-fsbi9cje9m.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ggfb0v5ylw", "Integrity": "ADH59/NtYFvapbe8WFHRcZlFJ2Si4p3FnbxP5fFT5lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12568, "LastWriteTime": "2025-05-24T10:56:12.5392844+00:00"}, "YmgMOS/ogHlIop5vai4oLmX0p52Byxw0kBYfpE5Jlnw=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\vz1dxm0tda-tmc1g35s3z.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=tmc1g35s3z}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8wl3mrbh96", "Integrity": "mKOek+hcGxc9JNCDfWknxZ1k1HnVlpIzrAjn/7Qovgc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3204, "LastWriteTime": "2025-05-24T10:56:12.5761358+00:00"}, "u9gQKsnytl6cE1y7PofPZESUhDhwajiYuF7ejQ2NQbw=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\22yenftene-fvhpjtyr6v.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5b7ig2cj79", "Integrity": "fzLlXEmw9871JGb59kzfItifyrM9MgjHNQirf4lIS64=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25714, "LastWriteTime": "2025-05-24T10:56:12.569629+00:00"}, "FhWHLeqgcpomeTWiUDeSQwhRTuEPuKyDpmvWeQ3jzG8=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\5gjnp28z46-qesaa3a1fm.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=qesaa3a1fm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yhemft0x44", "Integrity": "GsqAqFf0gorXCNSLhkvLgjTUzhvnj/r/ziu5TKmgRn8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3406, "LastWriteTime": "2025-05-24T10:56:12.543286+00:00"}, "4DHhHC9Wc/DWtsgqN70m0rSwX8Rrb304kYerH43Nb5U=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\gzxir09ed5-cosvhxvwiu.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f178oapzb7", "Integrity": "XVqmPhc00T/7zYDUfSy4bYaZ4YjTbapktP6tFnzH6xM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 14093, "LastWriteTime": "2025-05-24T10:56:12.5771454+00:00"}, "hBzZ2/lvOhnhu4DlrFDonpIV2YCFW3CHk2XaYGB91QQ=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\vkm14ixwgx-22vffe00uq.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=22vffe00uq}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1ki2uzdiv5", "Integrity": "cWQujyWwtYpOrAL/BVrmbuQuLebSh+MK7RHT7reHPL0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 6108, "LastWriteTime": "2025-05-24T10:56:12.5661077+00:00"}, "u1a5LNgr0JY2POuOzvt3USL4c7H5EtxlvBiFlWjMAEg=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\9g9fksc2vy-ausgxo2sd3.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lnst782kog", "Integrity": "ryK/tFqrYO6GpAIb5XFeLy8cUXDiklFAEkL+JelQZRo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 33061, "LastWriteTime": "2025-05-24T10:56:12.5801512+00:00"}, "1FxiS1MHRA3nUVJHoUH5ZC7L0MU70vgnVMPaTolADFA=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ekatybltws-xvp3kq03qx.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=xvp3kq03qx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z7y6dfz9j3", "Integrity": "vysQoe3FbcQ93VMRtTh9noOgIS/zfHwqe4MpEegkmNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6975, "LastWriteTime": "2025-05-24T10:56:12.5631078+00:00"}, "pkaBfoVqo+vYFEqyNyIknlnH9x3HbGmSTwBn3XrrtHE=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\wrdhmnlm9b-aexeepp0ev.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3latwtrz94", "Integrity": "1/7f+GraCXppQxdIoS5cuJ0z+sz2LI680XxYJg3fcdM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 14072, "LastWriteTime": "2025-05-24T10:56:12.5591078+00:00"}, "s26umhoorG86D205D48XGpHKpD93NNXRUav1hfyyCX0=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\e55pnj8i5z-sejl45xvog.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=sejl45xvog}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ru0tedr80l", "Integrity": "k3viZHtAtH1nURlGUHDkCuzFiiGm9hugvlQpwCBT5nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 6106, "LastWriteTime": "2025-05-24T10:56:12.5442839+00:00"}, "+OKe86qNEz0WAd/EtkBiXyg9CA47W4Ro5y4fI62hkxk=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\ov6srq43h2-c2jlpeoesf.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bpa5g0wlhg", "Integrity": "keojCSbIk+KcHuoejTbPFk8zgraA9s/7WmedJipnoMM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 33080, "LastWriteTime": "2025-05-24T10:56:12.5631078+00:00"}, "BhHKpNP7999B2sZpPI0Db2uA+q+aPDpFHc85V47xdF0=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\docid0nk50-t1cqhe9u97.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=t1cqhe9u97}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4t5zwq0ztq", "Integrity": "KJApOSMW9VEyvj1vwAXL6xmmIowXietNt8eeRzZOMrI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6973, "LastWriteTime": "2025-05-24T10:56:12.5402828+00:00"}, "JDDvvUwh4m/BuwRp7DBe9lGdZZxsqNbTcttVRCW7erI=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\5uu7ycx7eu-6wam8sj21o.gz", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "app#[.{fingerprint=6wam8sj21o}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "frzd7u0bj9", "Integrity": "S1Xg+Bi80O9cG6bLxJ7GogQxGxjuVCliyXjyNzOD1OQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\app.css", "FileLength": 1511, "LastWriteTime": "2025-05-24T10:56:12.5362663+00:00"}}, "CachedCopyCandidates": {}}