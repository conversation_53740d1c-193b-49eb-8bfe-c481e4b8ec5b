// <auto-generated/>

namespace Projects;

[global::System.CodeDom.Compiler.GeneratedCode("Aspire.Hosting", null)]
[global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage(Justification = "Generated code.")]
[global::System.Diagnostics.DebuggerDisplay("Type = {GetType().Name,nq}, ProjectPath = {ProjectPath}")]
public class TaskFlow_Web : global::Aspire.Hosting.IProjectMetadata
{
    public string ProjectPath => """C:\programok\TaskFlow\TaskFlow.Web\TaskFlow.Web.csproj""";
}
