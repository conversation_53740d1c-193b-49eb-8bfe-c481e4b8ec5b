//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute("49dbd3bc-5017-49f4-bb57-416601eec1ff")]
[assembly: System.Reflection.AssemblyMetadata("dcpclipath", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.orchestration.win-x64\\9.3.0\\tools\\d" +
    "cp.exe")]
[assembly: System.Reflection.AssemblyMetadata("dcpextensionpaths", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.orchestration.win-x64\\9.3.0\\tools\\e" +
    "xt\\")]
[assembly: System.Reflection.AssemblyMetadata("dcpbinpath", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.orchestration.win-x64\\9.3.0\\tools\\e" +
    "xt\\bin\\")]
[assembly: System.Reflection.AssemblyMetadata("apphostprojectpath", "C:\\programok\\TaskFlow\\TaskFlow.AppHost")]
[assembly: System.Reflection.AssemblyMetadata("apphostprojectname", "TaskFlow.AppHost.csproj")]
[assembly: System.Reflection.AssemblyMetadata("aspiredashboardpath", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.dashboard.sdk.win-x64\\9.3.0\\tools\\Aspire.Da" +
    "shboard.exe")]
[assembly: System.Reflection.AssemblyMetadataAttribute("apphostprojectbaseintermediateoutputpath", "C:\\programok\\TaskFlow\\TaskFlow.AppHost\\obj\\")]
[assembly: System.Reflection.AssemblyCompanyAttribute("TaskFlow.AppHost")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.0.0")]
[assembly: System.Reflection.AssemblyProductAttribute("TaskFlow.AppHost")]
[assembly: System.Reflection.AssemblyTitleAttribute("TaskFlow.AppHost")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]

// Generated by the MSBuild WriteCodeFragment class.

