{"Version": 1, "WorkspaceRootPath": "C:\\programok\\TaskFlow\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{D3732256-6309-E0B2-3988-73414621DCC2}|TaskFlow.Web\\TaskFlow.Web.csproj|c:\\programok\\taskflow\\taskflow.web\\components\\layout\\navmenu.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{D3732256-6309-E0B2-3988-73414621DCC2}|TaskFlow.Web\\TaskFlow.Web.csproj|solutionrelative:taskflow.web\\components\\layout\\navmenu.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{7D83C39A-E64A-4263-2CE2-AAA43B24C54F}|TaskFlow.ApiService\\TaskFlow.ApiService.csproj|c:\\programok\\taskflow\\taskflow.apiservice\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7D83C39A-E64A-4263-2CE2-AAA43B24C54F}|TaskFlow.ApiService\\TaskFlow.ApiService.csproj|solutionrelative:taskflow.apiservice\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7D83C39A-E64A-4263-2CE2-AAA43B24C54F}|TaskFlow.ApiService\\TaskFlow.ApiService.csproj|c:\\programok\\taskflow\\taskflow.apiservice\\data\\appdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7D83C39A-E64A-4263-2CE2-AAA43B24C54F}|TaskFlow.ApiService\\TaskFlow.ApiService.csproj|solutionrelative:taskflow.apiservice\\data\\appdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D3732256-6309-E0B2-3988-73414621DCC2}|TaskFlow.Web\\TaskFlow.Web.csproj|c:\\programok\\taskflow\\taskflow.web\\components\\pages\\projects.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{D3732256-6309-E0B2-3988-73414621DCC2}|TaskFlow.Web\\TaskFlow.Web.csproj|solutionrelative:taskflow.web\\components\\pages\\projects.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{D3732256-6309-E0B2-3988-73414621DCC2}|TaskFlow.Web\\TaskFlow.Web.csproj|c:\\programok\\taskflow\\taskflow.web\\components\\pages\\projectboard.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{D3732256-6309-E0B2-3988-73414621DCC2}|TaskFlow.Web\\TaskFlow.Web.csproj|solutionrelative:taskflow.web\\components\\pages\\projectboard.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{D3732256-6309-E0B2-3988-73414621DCC2}|TaskFlow.Web\\TaskFlow.Web.csproj|c:\\programok\\taskflow\\taskflow.web\\apiservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D3732256-6309-E0B2-3988-73414621DCC2}|TaskFlow.Web\\TaskFlow.Web.csproj|solutionrelative:taskflow.web\\apiservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D3732256-6309-E0B2-3988-73414621DCC2}|TaskFlow.Web\\TaskFlow.Web.csproj|c:\\programok\\taskflow\\taskflow.web\\models\\project.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D3732256-6309-E0B2-3988-73414621DCC2}|TaskFlow.Web\\TaskFlow.Web.csproj|solutionrelative:taskflow.web\\models\\project.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7D83C39A-E64A-4263-2CE2-AAA43B24C54F}|TaskFlow.ApiService\\TaskFlow.ApiService.csproj|c:\\programok\\taskflow\\taskflow.apiservice\\models\\sprint.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7D83C39A-E64A-4263-2CE2-AAA43B24C54F}|TaskFlow.ApiService\\TaskFlow.ApiService.csproj|solutionrelative:taskflow.apiservice\\models\\sprint.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7D83C39A-E64A-4263-2CE2-AAA43B24C54F}|TaskFlow.ApiService\\TaskFlow.ApiService.csproj|c:\\programok\\taskflow\\taskflow.apiservice\\models\\taskitem.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7D83C39A-E64A-4263-2CE2-AAA43B24C54F}|TaskFlow.ApiService\\TaskFlow.ApiService.csproj|solutionrelative:taskflow.apiservice\\models\\taskitem.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7D83C39A-E64A-4263-2CE2-AAA43B24C54F}|TaskFlow.ApiService\\TaskFlow.ApiService.csproj|c:\\programok\\taskflow\\taskflow.apiservice\\models\\project.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7D83C39A-E64A-4263-2CE2-AAA43B24C54F}|TaskFlow.ApiService\\TaskFlow.ApiService.csproj|solutionrelative:taskflow.apiservice\\models\\project.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Bookmark", "Name": "ST:1:0:{57d563b6-44a5-47df-85be-f4199ad6b651}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "NavMenu.razor", "DocumentMoniker": "C:\\programok\\TaskFlow\\TaskFlow.Web\\Components\\Layout\\NavMenu.razor", "RelativeDocumentMoniker": "TaskFlow.Web\\Components\\Layout\\NavMenu.razor", "ToolTip": "C:\\programok\\TaskFlow\\TaskFlow.Web\\Components\\Layout\\NavMenu.razor", "RelativeToolTip": "TaskFlow.Web\\Components\\Layout\\NavMenu.razor", "ViewState": "AgIAAAYAAAAAAAAAAAAAABIAAABJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-05-24T09:36:22.847Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "ProjectBoard.razor", "DocumentMoniker": "C:\\programok\\TaskFlow\\TaskFlow.Web\\Components\\Pages\\ProjectBoard.razor", "RelativeDocumentMoniker": "TaskFlow.Web\\Components\\Pages\\ProjectBoard.razor", "ToolTip": "C:\\programok\\TaskFlow\\TaskFlow.Web\\Components\\Pages\\ProjectBoard.razor", "RelativeToolTip": "TaskFlow.Web\\Components\\Pages\\ProjectBoard.razor", "ViewState": "AgIAAAUAAAAAAAAAAAAWwBEAAAAKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-05-24T09:35:55.672Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "Projects.razor", "DocumentMoniker": "C:\\programok\\TaskFlow\\TaskFlow.Web\\Components\\Pages\\Projects.razor", "RelativeDocumentMoniker": "TaskFlow.Web\\Components\\Pages\\Projects.razor", "ToolTip": "C:\\programok\\TaskFlow\\TaskFlow.Web\\Components\\Pages\\Projects.razor", "RelativeToolTip": "TaskFlow.Web\\Components\\Pages\\Projects.razor", "ViewState": "AgIAADgAAAAAAAAAAAAWwFEAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-05-24T09:35:35.997Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "Project.cs", "DocumentMoniker": "C:\\programok\\TaskFlow\\TaskFlow.Web\\Models\\Project.cs", "RelativeDocumentMoniker": "TaskFlow.Web\\Models\\Project.cs", "ToolTip": "C:\\programok\\TaskFlow\\TaskFlow.Web\\Models\\Project.cs", "RelativeToolTip": "TaskFlow.Web\\Models\\Project.cs", "ViewState": "AgIAAAcAAAAAAAAAAAA1wBEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-24T09:34:19.081Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "ApiService.cs", "DocumentMoniker": "C:\\programok\\TaskFlow\\TaskFlow.Web\\ApiService.cs", "RelativeDocumentMoniker": "TaskFlow.Web\\ApiService.cs", "ToolTip": "C:\\programok\\TaskFlow\\TaskFlow.Web\\ApiService.cs", "RelativeToolTip": "TaskFlow.Web\\ApiService.cs", "ViewState": "AgIAAEQAAAAAAAAAAAAswDkAAABEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-24T09:32:21.997Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "AppDbContext.cs", "DocumentMoniker": "C:\\programok\\TaskFlow\\TaskFlow.ApiService\\Data\\AppDbContext.cs", "RelativeDocumentMoniker": "TaskFlow.ApiService\\Data\\AppDbContext.cs", "ToolTip": "C:\\programok\\TaskFlow\\TaskFlow.ApiService\\Data\\AppDbContext.cs", "RelativeToolTip": "TaskFlow.ApiService\\Data\\AppDbContext.cs", "ViewState": "AgIAAAwAAAAAAAAAAAA5wAcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-24T08:54:51.92Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "Sprint.cs", "DocumentMoniker": "C:\\programok\\TaskFlow\\TaskFlow.ApiService\\Models\\Sprint.cs", "RelativeDocumentMoniker": "TaskFlow.ApiService\\Models\\Sprint.cs", "ToolTip": "C:\\programok\\TaskFlow\\TaskFlow.ApiService\\Models\\Sprint.cs", "RelativeToolTip": "TaskFlow.ApiService\\Models\\Sprint.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-24T08:53:14.893Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "TaskItem.cs", "DocumentMoniker": "C:\\programok\\TaskFlow\\TaskFlow.ApiService\\Models\\TaskItem.cs", "RelativeDocumentMoniker": "TaskFlow.ApiService\\Models\\TaskItem.cs", "ToolTip": "C:\\programok\\TaskFlow\\TaskFlow.ApiService\\Models\\TaskItem.cs", "RelativeToolTip": "TaskFlow.ApiService\\Models\\TaskItem.cs", "ViewState": "AgIAAB4AAAAAAAAAAAAUwBMAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-24T08:53:01.323Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "Project.cs", "DocumentMoniker": "C:\\programok\\TaskFlow\\TaskFlow.ApiService\\Models\\Project.cs", "RelativeDocumentMoniker": "TaskFlow.ApiService\\Models\\Project.cs", "ToolTip": "C:\\programok\\TaskFlow\\TaskFlow.ApiService\\Models\\Project.cs", "RelativeToolTip": "TaskFlow.ApiService\\Models\\Project.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-24T08:52:46.159Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Program.cs", "DocumentMoniker": "C:\\programok\\TaskFlow\\TaskFlow.ApiService\\Program.cs", "RelativeDocumentMoniker": "TaskFlow.ApiService\\Program.cs", "ToolTip": "C:\\programok\\TaskFlow\\TaskFlow.ApiService\\Program.cs", "RelativeToolTip": "TaskFlow.ApiService\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-24T08:51:55.705Z", "EditorCaption": ""}]}]}]}