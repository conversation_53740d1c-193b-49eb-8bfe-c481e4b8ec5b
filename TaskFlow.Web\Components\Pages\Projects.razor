﻿@page "/projects"
@using TaskFlow.Web.Models
@inject ApiService ApiClient
@inject NavigationManager NavigationManager
@rendermode InteractiveServer

<h3>Projects</h3>

@if (projects == null)
{
    <p><em>Loading projects...</em></p>
}
else
{
    <button @onclick="ShowCreateProjectModal">Create New Project</button>
    <table class="table">
        <thead>
            <tr>
                <th>Name</th>
                <th>Description</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var project in projects)
            {
                <tr>
                    <td>@project.Name</td>
                    <td>@project.Description</td>
                    <td>
                        <button @onclick="() => NavigateToProject(project.Id)">View Board</button>
                        @* Add Edit/Delete buttons here *@
                    </td>
                </tr>
            }
        </tbody>
    </table>
}

@if (showCreateModal)
{
    <div class="modal" tabindex="-1" style="display:block;" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create Project</h5>
                    <button type="button" class="btn-close" @onclick="CloseCreateProjectModal"></button>
                </div>
                <div class="modal-body">
                    <EditForm Model="newProject" OnValidSubmit="HandleCreateProject">
                        <DataAnnotationsValidator />
                        <div class="mb-3">
                            <label for="projectName" class="form-label">Project Name:</label>
                            <InputText id="projectName" class="form-control" @bind-Value="newProject.Name" />
                            <ValidationMessage For="@(() => newProject.Name)" />
                        </div>
                        <div class="mb-3">
                            <label for="projectDescription" class="form-label">Description:</label>
                            <InputTextArea id="projectDescription" class="form-control" @bind-Value="newProject.Description" />
                        </div>
                        <button type="submit" class="btn btn-primary">Save</button>
                        <button type="button" class="btn btn-secondary" @onclick="CloseCreateProjectModal">Cancel</button>
                    </EditForm>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
}

@code {
    private List<Project>? projects;
    private bool showCreateModal = false;
    private Project newProject = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadProjects();
    }

    private async Task LoadProjects()
    {
        projects = await ApiClient.GetProjectsAsync() ?? new List<Project>();
    }

    private void ShowCreateProjectModal()
    {
        newProject = new Project(); // Reset
        showCreateModal = true;
    }

    private void CloseCreateProjectModal() => showCreateModal = false;

    private async Task HandleCreateProject()
    {
        var created = await ApiClient.CreateProjectAsync(newProject);
        if (created != null)
        {
            await LoadProjects(); // Refresh list
            CloseCreateProjectModal();
        }
        // Else: handle error
    }

    private void NavigateToProject(int projectId)
    {
        NavigationManager.NavigateTo($"/project/{projectId}");
    }
}
