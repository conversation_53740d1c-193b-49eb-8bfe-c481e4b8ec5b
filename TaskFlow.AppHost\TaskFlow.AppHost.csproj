<Project Sdk="Microsoft.NET.Sdk">
  <Sdk Name="Aspire.AppHost.Sdk" Version="9.3.0" />
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsAspireHost>true</IsAspireHost>
    <UserSecretsId>49dbd3bc-5017-49f4-bb57-416601eec1ff</UserSecretsId>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\TaskFlow.ApiService\TaskFlow.ApiService.csproj" />
    <ProjectReference Include="..\TaskFlow.Web\TaskFlow.Web.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.AppHost" />
  </ItemGroup>
</Project>