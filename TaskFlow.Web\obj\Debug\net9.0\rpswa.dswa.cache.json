{"GlobalPropertiesHash": "sTZubqVa2Q0gVyUj5zDez5jjg1z9Qgo9UhsYNY7XA5w=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["UBUDwta0T+mdXUXJWM7TX4/rMNXDyyNleKDGpDoa454=", "2b+cSqUF6Q18/4sNa9CkvhQZNKsyNW/Ut3YoKk++Yu0=", "AIp2rtCJ+MT2kYr1V7WbLSBfWPfYh44LX5GUwyWpXMA=", "ReTDbjlRH76GPfZI+gdtqOsBDGrfbxVxbEJRTCeER4E=", "IgkyZKRvSgn/K0ZkTdFMJrRjSgrAKNxrQA/uuqiXlas=", "vFU9LS69d4Rd++LTukHL2iQWmn3tAODsl0RoynHye2c=", "iTGIoojvc8gr07u230ORI7XQwoGhZ2rGXj33CD1fwxQ=", "zgKOcVjcug92va7zEpp1TG85GuYqdOhiU5pSEjzzP4E=", "xy5FN6GztX2Aa0RNTI3AcIHxFupslWxUj1GEl7LhCQU=", "IxI2Yvid45zhNVVc5sokY17m+qVb1Q3pG591wNr9n0c=", "MkWKbVTQTvJKIQGNRaMkiyx1QNmgNlV2cUlLwdX8rY4=", "/ouLwySLygykpeyLsu7BH0tTEBd7ZVwmhpI6jOXSU1M=", "FR1LyRnNzTqNUX5tZWNqFmIQgCuhlJucdfFBbb87/6c=", "Bpu0K/k65/5q08VQap2r8CbMgTEEMsf8fTJurid4he4=", "RW3c2DhZ9fBfQtXWvW8Rky+NiDMmrBUCLBa1LPMyBJk=", "K0ssLGO6VdCiJeAFA4dJY2u87nlGZdhRhPcDoygFv/c=", "bJCj6l6tciUJGpYttgAs1Azy4ujn4Vxqayj1yMfO1q8=", "kr+rZV/KCW0N9ZwReQUkKmLmocX+E7DbpqZ+eo02FV0=", "uPphi7JxhWm2jh0gCxnbSEXP5vcJ2v1/k/vo3IJ/Ac8=", "lBsPQasXZY2mFzwX2kybFlSCb5Ec96NJBcWHl5xozlg=", "X/qz5Tbt/LGdcPCV4TFyUvFstJ3kiJTyFeHxkIW8IxE=", "SSqEx/WbE9UXBbwcwHEfL01ufDHmnZVcN+HA6CE1FsE=", "p4FJOJuKo/Xd+CwwqmipbOAXDB5txcCQXn8kQjFnlPU=", "XLRRM5aCI5l1kQLYCLZWTFP6oIXsODmNuwjL5cdS7co=", "KtzUQG8H12Zww8hDt1GQKAAnh5GwJnJEdwxyXc5nTrU=", "HIKghj4PNOW4AK8dcZ/fx/2+98OUtZDnca5GkTtrlLg=", "DJ4wDaQPcVFZL7CK8PBVB/bHQKfJ4uoh4R6Ihxmb2UQ=", "3d9krIMUsNFmBOSMfIAIPRMKryNaXVEFkEbWDlW8/dM=", "/PF2LBRIBKUUztAZSfCABdS1Ueu230putIKO6mlrR54=", "S1YRPk4O/y6BINNya2thkXVyQUHyj+r2XU9vVjwx7EM=", "KE/lR/CQhtjjbbQnlzKeif9pyMLLdz8YCiOxobhHynA=", "6ZFvzj+m4tjPmTIgHjgHlgi2RdCyJ/dg7vypdwe2vTQ=", "4b4g+iC8rThl8dxTo2Rw1sXUCSXoTcdg2sTBeiG6xJs=", "DAzeBvrmkoDsM+L+U+xBc8L6bul+8DQk9LHbEQAyoLk=", "DRdTFei1LNUmib+82mT2ZUfnCW6Jm0RuVslsnrNrbv4=", "LllaUVnlQ786AsJF5H+BMNilhB2eyYqp/cTmXyrQiNM=", "M2ndgd6r4RmrZzrmu8NgeF/4ZF1gdgKjaA5uGAKJzSA=", "5Y5/ErlGwE2NqJ0LDmDh89nsYh/exmNJdWg1McrA3l8=", "ocx0czYtEOuElhfpvPr6Aj+3WksLZG48SRy+HOd6c0o=", "HC46JhkaZ6s0PToOuQGLKPHhHPC0AFIjfJ1iGKPusr4=", "i0vWYIhlFoWja5QJPVLQAxshUpMyX7F+HZao84cbLLc=", "v49meFA205QunG5Tw5VytJKT9Az1xojQifrzjPjwt0Q=", "hmGrKeU+gV8L9kMrHxJTpaU3AqdVYAQjsowJ8ORdpZo=", "xBfNsW0rjaH98Ay1M5rP81D7kUY++rTJ8k0J7Qq/SgI=", "mtlgOvXu+Q5TZDK/Z8muzUR4/fqjJns6gY4UGzT+Yd0=", "N6oO/M5Me9zCp2HYpwCNaL2J4X0MGSoXD6v5f0j3hu4=", "ZDervy9KABlCFcJN5pY5RE12tJfhNoqXYTVcti3dBbk=", "2d+tGKvqAornkp9qPq/YPHtQ/EMiPTdnb9k7rr84zvY=", "Rbq5eV7pnxMLr3VipCSlTKukDIqGLcMKsMw885VUur0=", "EEVZ5UAWB60r0xgABNgu4+jywoogBPHxmt/Y7icqgvg=", "QVooKXWqlv0CyQhBvUh5Y4sprBb7v4OH+PAonC6iTW8=", "caxmyzlLbycUgbDwj9u3dAYUCHiuu7fjRmHu85P0kPM=", "TPxkHsLnQD+bbKgt5T5PGSNmLBdKJCiS6RgKUxQJGas=", "V+T9ho5Y1PFSI7TXSPP73YSkNqK2dUkXR2o9cleeu+0=", "A59yY2bORJD9jU/XHguWbmy6h9E41snEGSCoou8w2Uw=", "S+ngmKVEe9YDZb/Wkh9DQGfimI21OUms8BOuYurDQ7g=", "9QW8JJadzhb7caQ2ztF9ofkWFEWlUZbG9TfCDWJcj10=", "Ls5QlP0qNhCaeJ1pUJDpNFkxl1TTjrYIFfL1OH0ZiU0=", "Dk3TgRpG1UHOT+sNxbCc9F7FOoeVPZUZEADKRMMB8M8=", "yTn65eIvw4ZLP9Gd6sJVnMSaxcc6WWmTmE4Ion7edSA=", "ThVDIS1FdoJy74/7Xq3zyOtvE5uApYRIGDOUhRyUZLk="], "CachedAssets": {"UBUDwta0T+mdXUXJWM7TX4/rMNXDyyNleKDGpDoa454=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\app.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6wam8sj21o", "Integrity": "Wndt+V4Ynjm1ybKbGOiJLvIuWDuYYgNu3Cu400cBtzM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css", "FileLength": 2825, "LastWriteTime": "2025-05-24T08:37:47.252488+00:00"}, "2b+cSqUF6Q18/4sNa9CkvhQZNKsyNW/Ut3YoKk++Yu0=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\favicon.png", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-05-24T08:37:47.2545337+00:00"}, "AIp2rtCJ+MT2kYr1V7WbLSBfWPfYh44LX5GUwyWpXMA=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "t1cqhe9u97", "Integrity": "f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 74413, "LastWriteTime": "2025-05-24T08:37:47.2590655+00:00"}, "ReTDbjlRH76GPfZI+gdtqOsBDGrfbxVxbEJRTCeER4E=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-05-24T08:37:47.2666369+00:00"}, "IgkyZKRvSgn/K0ZkTdFMJrRjSgrAKNxrQA/uuqiXlas=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sejl45xvog", "Integrity": "hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51800, "LastWriteTime": "2025-05-24T08:37:47.2698105+00:00"}, "vFU9LS69d4Rd++LTukHL2iQWmn3tAODsl0RoynHye2c=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-05-24T08:37:47.2761086+00:00"}, "iTGIoojvc8gr07u230ORI7XQwoGhZ2rGXj33CD1fwxQ=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xvp3kq03qx", "Integrity": "M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 74486, "LastWriteTime": "2025-05-24T08:37:47.2805903+00:00"}, "zgKOcVjcug92va7zEpp1TG85GuYqdOhiU5pSEjzzP4E=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-05-24T08:37:47.2889581+00:00"}, "xy5FN6GztX2Aa0RNTI3AcIHxFupslWxUj1GEl7LhCQU=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "22vffe00uq", "Integrity": "+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51875, "LastWriteTime": "2025-05-24T08:37:47.291475+00:00"}, "IxI2Yvid45zhNVVc5sokY17m+qVb1Q3pG591wNr9n0c=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-05-24T08:37:47.301386+00:00"}, "MkWKbVTQTvJKIQGNRaMkiyx1QNmgNlV2cUlLwdX8rY4=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qesaa3a1fm", "Integrity": "rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12661, "LastWriteTime": "2025-05-24T08:37:47.3025634+00:00"}, "/ouLwySLygykpeyLsu7BH0tTEBd7ZVwmhpI6jOXSU1M=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-05-24T08:37:47.3094459+00:00"}, "FR1LyRnNzTqNUX5tZWNqFmIQgCuhlJucdfFBbb87/6c=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tmc1g35s3z", "Integrity": "y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10131, "LastWriteTime": "2025-05-24T08:37:47.3114486+00:00"}, "Bpu0K/k65/5q08VQap2r8CbMgTEEMsf8fTJurid4he4=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-05-24T08:37:47.3140011+00:00"}, "RW3c2DhZ9fBfQtXWvW8Rky+NiDMmrBUCLBa1LPMyBJk=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rxsg74s51o", "Integrity": "gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12651, "LastWriteTime": "2025-05-24T08:37:47.3160842+00:00"}, "K0ssLGO6VdCiJeAFA4dJY2u87nlGZdhRhPcDoygFv/c=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-05-24T08:37:47.3218774+00:00"}, "bJCj6l6tciUJGpYttgAs1Azy4ujn4Vxqayj1yMfO1q8=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q9ht133ko3", "Integrity": "UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10203, "LastWriteTime": "2025-05-24T08:37:47.3243285+00:00"}, "kr+rZV/KCW0N9ZwReQUkKmLmocX+E7DbpqZ+eo02FV0=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-05-24T08:37:47.327728+00:00"}, "uPphi7JxhWm2jh0gCxnbSEXP5vcJ2v1/k/vo3IJ/Ac8=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gye83jo8yx", "Integrity": "uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 113224, "LastWriteTime": "2025-05-24T08:37:47.3333038+00:00"}, "lBsPQasXZY2mFzwX2kybFlSCb5Ec96NJBcWHl5xozlg=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-05-24T08:37:47.3432153+00:00"}, "X/qz5Tbt/LGdcPCV4TFyUvFstJ3kiJTyFeHxkIW8IxE=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wl58j5mj3v", "Integrity": "ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85357, "LastWriteTime": "2025-05-24T08:37:47.3472152+00:00"}, "SSqEx/WbE9UXBbwcwHEfL01ufDHmnZVcN+HA6CE1FsE=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-05-24T08:37:47.3550246+00:00"}, "p4FJOJuKo/Xd+CwwqmipbOAXDB5txcCQXn8kQjFnlPU=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d4r6k3f320", "Integrity": "MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 113083, "LastWriteTime": "2025-05-24T08:37:47.3595037+00:00"}, "XLRRM5aCI5l1kQLYCLZWTFP6oIXsODmNuwjL5cdS7co=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-05-24T08:37:47.3739656+00:00"}, "KtzUQG8H12Zww8hDt1GQKAAnh5GwJnJEdwxyXc5nTrU=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "keugtjm085", "Integrity": "7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85286, "LastWriteTime": "2025-05-24T08:37:47.3784199+00:00"}, "HIKghj4PNOW4AK8dcZ/fx/2+98OUtZDnca5GkTtrlLg=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-05-24T08:37:47.3849274+00:00"}, "DJ4wDaQPcVFZL7CK8PBVB/bHQKfJ4uoh4R6Ihxmb2UQ=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zub09dkrxp", "Integrity": "CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 293102, "LastWriteTime": "2025-05-24T08:37:47.3955786+00:00"}, "3d9krIMUsNFmBOSMfIAIPRMKryNaXVEFkEbWDlW8/dM=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-05-24T08:37:47.4179641+00:00"}, "/PF2LBRIBKUUztAZSfCABdS1Ueu230putIKO6mlrR54=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "43atpzeawx", "Integrity": "sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232808, "LastWriteTime": "2025-05-24T08:37:47.4254724+00:00"}, "S1YRPk4O/y6BINNya2thkXVyQUHyj+r2XU9vVjwx7EM=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-05-24T08:37:47.4453127+00:00"}, "KE/lR/CQhtjjbbQnlzKeif9pyMLLdz8YCiOxobhHynA=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ynyaa8k90p", "Integrity": "/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 292288, "LastWriteTime": "2025-05-24T08:37:47.4558187+00:00"}, "6ZFvzj+m4tjPmTIgHjgHlgi2RdCyJ/dg7vypdwe2vTQ=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-05-24T08:37:47.4787467+00:00"}, "4b4g+iC8rThl8dxTo2Rw1sXUCSXoTcdg2sTBeiG6xJs=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c63t5i9ira", "Integrity": "rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232916, "LastWriteTime": "2025-05-24T08:37:47.5077799+00:00"}, "DAzeBvrmkoDsM+L+U+xBc8L6bul+8DQk9LHbEQAyoLk=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-05-24T08:37:47.5313198+00:00"}, "DRdTFei1LNUmib+82mT2ZUfnCW6Jm0RuVslsnrNrbv4=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aak5eirmym", "Integrity": "DoRbpT+MFNf8Kw514KCM7HP0Xkm7mSIlvI2H77dfRDA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 231707, "LastWriteTime": "2025-05-24T08:37:47.5423505+00:00"}, "LllaUVnlQ786AsJF5H+BMNilhB2eyYqp/cTmXyrQiNM=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m8m04ow07k", "Integrity": "llRPAjsTCQdX9I6jAgtmhr1rUT9tvAVdaPQrYD38Td4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-05-24T08:37:47.5595128+00:00"}, "M2ndgd6r4RmrZzrmu8NgeF/4ZF1gdgKjaA5uGAKJzSA=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sebe1i1n8x", "Integrity": "RCqG8kOYgTkEN6lTuil29qaURdLpMdgr2x1tKpEn2Ow=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 98317, "LastWriteTime": "2025-05-24T08:37:47.5663768+00:00"}, "5Y5/ErlGwE2NqJ0LDmDh89nsYh/exmNJdWg1McrA3l8=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "07r8e49zdp", "Integrity": "rOniCgnoNJIVZp7PCJnzSGta8mgTGweOpxp/z4+Oc+k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-05-24T08:37:47.5795078+00:00"}, "ocx0czYtEOuElhfpvPr6Aj+3WksLZG48SRy+HOd6c0o=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xy033osspr", "Integrity": "V9VHrfWTQdDZhpDjgAzkkk7JIEn/cx3lMtjiDUXul/Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 157850, "LastWriteTime": "2025-05-24T08:37:47.5863944+00:00"}, "HC46JhkaZ6s0PToOuQGLKPHhHPC0AFIjfJ1iGKPusr4=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sybl3t77fa", "Integrity": "1NZ0e0qqYzJigO5XFb9Y2bgUwRxi1f/jpwe4eU6A5pA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-05-24T08:37:47.5992607+00:00"}, "i0vWYIhlFoWja5QJPVLQAxshUpMyX7F+HZao84cbLLc=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l53j0jwsua", "Integrity": "Rz2BsZTYP8L0/pkMgZIAMFzaFfsMWc+8175AqV69XCE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 91499, "LastWriteTime": "2025-05-24T08:37:47.604906+00:00"}, "v49meFA205QunG5Tw5VytJKT9Az1xojQifrzjPjwt0Q=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u3orf5n9fb", "Integrity": "ygX//9b5IsEzbh1wXO6jfbN5R0YVHtui5Zm2Pxzfows=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-05-24T08:37:47.6147354+00:00"}, "hmGrKeU+gV8L9kMrHxJTpaU3AqdVYAQjsowJ8ORdpZo=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "1jo1kezdci", "Integrity": "k9Hcnfw1uBvIz3LBQJE2enuHAQ+2vcGQx7FY/sjMCT0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 167465, "LastWriteTime": "2025-05-24T08:37:47.6272489+00:00"}, "xBfNsW0rjaH98Ay1M5rP81D7kUY++rTJ8k0J7Qq/SgI=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xxsbxg4q4i", "Integrity": "u9e0mnJh0mj/d32Mx6N9Ibj/0+LtjionqG5TWUyotPk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-05-24T08:37:47.641237+00:00"}, "mtlgOvXu+Q5TZDK/Z8muzUR4/fqjJns6gY4UGzT+Yd0=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iug4zs40ca", "Integrity": "tp7hlG6xiG3KkONtF2Z5esr5FTcW2r83xX3hex8WMX8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 78199, "LastWriteTime": "2025-05-24T08:37:47.6447526+00:00"}, "N6oO/M5Me9zCp2HYpwCNaL2J4X0MGSoXD6v5f0j3hu4=": {"Identity": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "TaskFlow.Web", "SourceType": "Discovered", "ContentRoot": "C:\\programok\\TaskFlow\\TaskFlow.Web\\wwwroot\\", "BasePath": "_content/TaskFlow.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "knl5pustf3", "Integrity": "IBIemhxt8rrB7DVR6DoN0KwZECFkw69mvMSn40GJXsc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-05-24T08:37:47.6597089+00:00"}}, "CachedCopyCandidates": {}}