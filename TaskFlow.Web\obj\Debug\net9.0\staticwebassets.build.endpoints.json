{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "TaskFlow.Web.khv3du26ro.styles.css", "AssetFile": "TaskFlow.Web.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000574052813"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1741"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2bj+yQE2YI1QzLnFdJ8MIsiCxthylp2AwB1vfqLMHdY=\""}, {"Name": "ETag", "Value": "W/\"KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "khv3du26ro"}, {"Name": "integrity", "Value": "sha256-KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ="}, {"Name": "label", "Value": "TaskFlow.Web.styles.css"}]}, {"Route": "TaskFlow.Web.khv3du26ro.styles.css", "AssetFile": "TaskFlow.Web.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5796"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "khv3du26ro"}, {"Name": "integrity", "Value": "sha256-KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ="}, {"Name": "label", "Value": "TaskFlow.Web.styles.css"}]}, {"Route": "TaskFlow.Web.khv3du26ro.styles.css.gz", "AssetFile": "TaskFlow.Web.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1741"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2bj+yQE2YI1QzLnFdJ8MIsiCxthylp2AwB1vfqLMHdY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "khv3du26ro"}, {"Name": "integrity", "Value": "sha256-2bj+yQE2YI1QzLnFdJ8MIsiCxthylp2AwB1vfqLMHdY="}, {"Name": "label", "Value": "TaskFlow.Web.styles.css.gz"}]}, {"Route": "TaskFlow.Web.styles.css", "AssetFile": "TaskFlow.Web.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000574052813"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1741"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2bj+yQE2YI1QzLnFdJ8MIsiCxthylp2AwB1vfqLMHdY=\""}, {"Name": "ETag", "Value": "W/\"KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ="}]}, {"Route": "TaskFlow.Web.styles.css", "AssetFile": "TaskFlow.Web.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5796"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KMd9mhQUPkptVyML7+LYEPz/KM9AXdMhpnnpcIzqNrQ="}]}, {"Route": "TaskFlow.Web.styles.css.gz", "AssetFile": "TaskFlow.Web.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1741"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2bj+yQE2YI1QzLnFdJ8MIsiCxthylp2AwB1vfqLMHdY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2bj+yQE2YI1QzLnFdJ8MIsiCxthylp2AwB1vfqLMHdY="}]}, {"Route": "app.6wam8sj21o.css", "AssetFile": "app.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000644329897"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1551"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xxiqA3yGYGiMCcyDlArcfhUmX1F6WqHu42fpwkjflCo=\""}, {"Name": "ETag", "Value": "W/\"Wndt+V4Ynjm1ybKbGOiJLvIuWDuYYgNu3Cu400cBtzM=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6wam8sj21o"}, {"Name": "integrity", "Value": "sha256-Wndt+V4Ynjm1ybKbGOiJLvIuWDuYYgNu3Cu400cBtzM="}, {"Name": "label", "Value": "app.css"}]}, {"Route": "app.6wam8sj21o.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2825"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Wndt+V4Ynjm1ybKbGOiJLvIuWDuYYgNu3Cu400cBtzM=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6wam8sj21o"}, {"Name": "integrity", "Value": "sha256-Wndt+V4Ynjm1ybKbGOiJLvIuWDuYYgNu3Cu400cBtzM="}, {"Name": "label", "Value": "app.css"}]}, {"Route": "app.6wam8sj21o.css.gz", "AssetFile": "app.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1551"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xxiqA3yGYGiMCcyDlArcfhUmX1F6WqHu42fpwkjflCo=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6wam8sj21o"}, {"Name": "integrity", "Value": "sha256-xxiqA3yGYGiMCcyDlArcfhUmX1F6WqHu42fpwkjflCo="}, {"Name": "label", "Value": "app.css.gz"}]}, {"Route": "app.css", "AssetFile": "app.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000644329897"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1551"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xxiqA3yGYGiMCcyDlArcfhUmX1F6WqHu42fpwkjflCo=\""}, {"Name": "ETag", "Value": "W/\"Wndt+V4Ynjm1ybKbGOiJLvIuWDuYYgNu3Cu400cBtzM=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Wndt+V4Ynjm1ybKbGOiJLvIuWDuYYgNu3Cu400cBtzM="}]}, {"Route": "app.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2825"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Wndt+V4Ynjm1ybKbGOiJLvIuWDuYYgNu3Cu400cBtzM=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Wndt+V4Ynjm1ybKbGOiJLvIuWDuYYgNu3Cu400cBtzM="}]}, {"Route": "app.css.gz", "AssetFile": "app.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1551"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xxiqA3yGYGiMCcyDlArcfhUmX1F6WqHu42fpwkjflCo=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xxiqA3yGYGiMCcyDlArcfhUmX1F6WqHu42fpwkjflCo="}]}, {"Route": "favicon.ifv42okdf2.png", "AssetFile": "favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ifv42okdf2"}, {"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}, {"Name": "label", "Value": "favicon.png"}]}, {"Route": "favicon.png", "AssetFile": "favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148038490"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6754"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"td/WnErsJHKP3LT8Bz6znCdABuCWnP513FYSQpHRK9A=\""}, {"Name": "ETag", "Value": "W/\"f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "74413"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.c2jlpeoesf.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030492453"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32794"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=\""}, {"Name": "ETag", "Value": "W/\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2j<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.c2jlpeoesf.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "203221"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2j<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.c2jlpeoesf.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32794"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2j<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6754"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"td/WnErsJHKP3LT8Bz6znCdABuCWnP513FYSQpHRK9A=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-td/WnErsJHKP3LT8Bz6znCdABuCWnP513FYSQpHRK9A="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030492453"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32794"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=\""}, {"Name": "ETag", "Value": "W/\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "203221"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32794"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167392032"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5973"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2psAeYsQwIU+4hjhaa7aiuEI8eM0VqXG4mLMI2LB5kg=\""}, {"Name": "ETag", "Value": "W/\"hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51800"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.aexeepp0ev.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072421784"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=\""}, {"Name": "ETag", "Value": "W/\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aexeepp0ev"}, {"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.aexeepp0ev.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "115986"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aexeepp0ev"}, {"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.aexeepp0ev.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aexeepp0ev"}, {"Name": "integrity", "Value": "sha256-oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5973"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2psAeYsQwIU+4hjhaa7aiuEI8eM0VqXG4mLMI2LB5kg=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2psAeYsQwIU+4hjhaa7aiuEI8eM0VqXG4mLMI2LB5kg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072421784"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=\""}, {"Name": "ETag", "Value": "W/\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "115986"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.sejl45xvog.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167392032"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5973"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2psAeYsQwIU+4hjhaa7aiuEI8eM0VqXG4mLMI2LB5kg=\""}, {"Name": "ETag", "Value": "W/\"hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sejl45xvog"}, {"Name": "integrity", "Value": "sha256-hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.sejl45xvog.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "51800"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sejl45xvog"}, {"Name": "integrity", "Value": "sha256-hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.sejl45xvog.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5973"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2psAeYsQwIU+4hjhaa7aiuEI8eM0VqXG4mLMI2LB5kg=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sejl45xvog"}, {"Name": "integrity", "Value": "sha256-2psAeYsQwIU+4hjhaa7aiuEI8eM0VqXG4mLMI2LB5kg="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148016578"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6755"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Jb3hghPX8HIvLINQ2LRDLH33mFfqyzbk+nTsbiHtKr4=\""}, {"Name": "ETag", "Value": "W/\"M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "74486"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.ausgxo2sd3.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030493383"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32793"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=\""}, {"Name": "ETag", "Value": "W/\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausgxo2sd3"}, {"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.ausgxo2sd3.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "203225"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausgxo2sd3"}, {"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.ausgxo2sd3.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32793"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausgxo2sd3"}, {"Name": "integrity", "Value": "sha256-cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6755"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Jb3hghPX8HIvLINQ2LRDLH33mFfqyzbk+nTsbiHtKr4=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jb3hghPX8HIvLINQ2LRDLH33mFfqyzbk+nTsbiHtKr4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030493383"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32793"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=\""}, {"Name": "ETag", "Value": "W/\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "203225"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32793"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.22vffe00uq.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167308014"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5976"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Gjkg0hmhA9Aga0f2ZS8ZqJ/GES4kxIvtWTfabr1bc2Y=\""}, {"Name": "ETag", "Value": "W/\"+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "22vffe00uq"}, {"Name": "integrity", "Value": "sha256-+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.22vffe00uq.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "51875"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "22vffe00uq"}, {"Name": "integrity", "Value": "sha256-+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.22vffe00uq.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5976"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Gjkg0hmhA9Aga0f2ZS8ZqJ/GES4kxIvtWTfabr1bc2Y=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "22vffe00uq"}, {"Name": "integrity", "Value": "sha256-Gjkg0hmhA9Aga0f2ZS8ZqJ/GES4kxIvtWTfabr1bc2Y="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167308014"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5976"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Gjkg0hmhA9Aga0f2ZS8ZqJ/GES4kxIvtWTfabr1bc2Y=\""}, {"Name": "ETag", "Value": "W/\"+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51875"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.cosvhxvwiu.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072379849"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13815"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=\""}, {"Name": "ETag", "Value": "W/\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cosvhxvwiu"}, {"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.cosvhxvwiu.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "116063"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cosvhxvwiu"}, {"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.cosvhxvwiu.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13815"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cosvhxvwiu"}, {"Name": "integrity", "Value": "sha256-V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5976"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Gjkg0hmhA9Aga0f2ZS8ZqJ/GES4kxIvtWTfabr1bc2Y=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Gjkg0hmhA9Aga0f2ZS8ZqJ/GES4kxIvtWTfabr1bc2Y="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072379849"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13815"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=\""}, {"Name": "ETag", "Value": "W/\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "116063"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13815"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.xvp3kq03qx.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148016578"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6755"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Jb3hghPX8HIvLINQ2LRDLH33mFfqyzbk+nTsbiHtKr4=\""}, {"Name": "ETag", "Value": "W/\"M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xvp3kq03qx"}, {"Name": "integrity", "Value": "sha256-M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.xvp3kq03qx.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "74486"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xvp3kq03qx"}, {"Name": "integrity", "Value": "sha256-M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.xvp3kq03qx.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6755"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Jb3hghPX8HIvLINQ2LRDLH33mFfqyzbk+nTsbiHtKr4=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xvp3kq03qx"}, {"Name": "integrity", "Value": "sha256-Jb3hghPX8HIvLINQ2LRDLH33mFfqyzbk+nTsbiHtKr4="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.t1cqhe9u97.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148038490"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6754"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"td/WnErsJHKP3LT8Bz6znCdABuCWnP513FYSQpHRK9A=\""}, {"Name": "ETag", "Value": "W/\"f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t1cqhe9u97"}, {"Name": "integrity", "Value": "sha256-f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.t1cqhe9u97.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "74413"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t1cqhe9u97"}, {"Name": "integrity", "Value": "sha256-f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.t1cqhe9u97.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6754"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"td/WnErsJHKP3LT8Bz6znCdABuCWnP513FYSQpHRK9A=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t1cqhe9u97"}, {"Name": "integrity", "Value": "sha256-td/WnErsJHKP3LT8Bz6znCdABuCWnP513FYSQpHRK9A="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000293427230"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3407"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"H9IPXBzUtJGG7PJdjNTz1WD7YcFYCi+22bHkHPR+CdY=\""}, {"Name": "ETag", "Value": "W/\"rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12661"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.fvhpjtyr6v.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038726667"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25821"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=\""}, {"Name": "ETag", "Value": "W/\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fvhpjtyr6v"}, {"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.fvhpjtyr6v.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "129371"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fvhpjtyr6v"}, {"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.fvhpjtyr6v.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25821"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fvhpjtyr6v"}, {"Name": "integrity", "Value": "sha256-FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3407"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"H9IPXBzUtJGG7PJdjNTz1WD7YcFYCi+22bHkHPR+CdY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H9IPXBzUtJGG7PJdjNTz1WD7YcFYCi+22bHkHPR+CdY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038726667"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25821"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=\""}, {"Name": "ETag", "Value": "W/\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "129371"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25821"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000310752020"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3217"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"w8PM3Ms+TEJVQ/PGIsRL/kn0h83Pm5ek8Sm7/zkgCYc=\""}, {"Name": "ETag", "Value": "W/\"y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10131"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.fsbi9cje9m.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000079440737"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12587"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=\""}, {"Name": "ETag", "Value": "W/\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fsbi9cje9m"}, {"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.fsbi9cje9m.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "51369"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fsbi9cje9m"}, {"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.fsbi9cje9m.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12587"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fsbi9cje9m"}, {"Name": "integrity", "Value": "sha256-crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3217"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"w8PM3Ms+TEJVQ/PGIsRL/kn0h83Pm5ek8Sm7/zkgCYc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-w8PM3Ms+TEJVQ/PGIsRL/kn0h83Pm5ek8Sm7/zkgCYc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000079440737"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12587"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=\""}, {"Name": "ETag", "Value": "W/\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51369"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12587"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.tmc1g35s3z.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000310752020"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3217"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"w8PM3Ms+TEJVQ/PGIsRL/kn0h83Pm5ek8Sm7/zkgCYc=\""}, {"Name": "ETag", "Value": "W/\"y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tmc1g35s3z"}, {"Name": "integrity", "Value": "sha256-y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.tmc1g35s3z.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10131"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tmc1g35s3z"}, {"Name": "integrity", "Value": "sha256-y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.tmc1g35s3z.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3217"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"w8PM3Ms+TEJVQ/PGIsRL/kn0h83Pm5ek8Sm7/zkgCYc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tmc1g35s3z"}, {"Name": "integrity", "Value": "sha256-w8PM3Ms+TEJVQ/PGIsRL/kn0h83Pm5ek8Sm7/zkgCYc="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.qesaa3a1fm.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000293427230"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3407"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"H9IPXBzUtJGG7PJdjNTz1WD7YcFYCi+22bHkHPR+CdY=\""}, {"Name": "ETag", "Value": "W/\"rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qesaa3a1fm"}, {"Name": "integrity", "Value": "sha256-rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.qesaa3a1fm.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "12661"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qesaa3a1fm"}, {"Name": "integrity", "Value": "sha256-rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.qesaa3a1fm.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3407"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"H9IPXBzUtJGG7PJdjNTz1WD7YcFYCi+22bHkHPR+CdY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qesaa3a1fm"}, {"Name": "integrity", "Value": "sha256-H9IPXBzUtJGG7PJdjNTz1WD7YcFYCi+22bHkHPR+CdY="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000294290759"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3397"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ueerZxLdSKIEsWJ13wzBfTcRfd3uGSfOQwhUfM1jRec=\""}, {"Name": "ETag", "Value": "W/\"gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12651"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.ee0r1s7dh0.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038708678"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25833"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=\""}, {"Name": "ETag", "Value": "W/\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ee0r1s7dh0"}, {"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.ee0r1s7dh0.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "129386"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ee0r1s7dh0"}, {"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.ee0r1s7dh0.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25833"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ee0r1s7dh0"}, {"Name": "integrity", "Value": "sha256-wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3397"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ueerZxLdSKIEsWJ13wzBfTcRfd3uGSfOQwhUfM1jRec=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ueerZxLdSKIEsWJ13wzBfTcRfd3uGSfOQwhUfM1jRec="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038708678"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25833"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=\""}, {"Name": "ETag", "Value": "W/\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "129386"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25833"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000307597662"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3250"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AX9UjSDaqolhmzTa/KUDWMjLF2XlYT6I6JZuUGrm1nw=\""}, {"Name": "ETag", "Value": "W/\"UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10203"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3250"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AX9UjSDaqolhmzTa/KUDWMjLF2XlYT6I6JZuUGrm1nw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AX9UjSDaqolhmzTa/KUDWMjLF2XlYT6I6JZuUGrm1nw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.jd9uben2k1.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000066423115"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15054"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=\""}, {"Name": "ETag", "Value": "W/\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jd9uben2k1"}, {"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.jd9uben2k1.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "63943"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jd9uben2k1"}, {"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.jd9uben2k1.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15054"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jd9uben2k1"}, {"Name": "integrity", "Value": "sha256-V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000066423115"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15054"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=\""}, {"Name": "ETag", "Value": "W/\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "63943"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15054"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.q9ht133ko3.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000307597662"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3250"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AX9UjSDaqolhmzTa/KUDWMjLF2XlYT6I6JZuUGrm1nw=\""}, {"Name": "ETag", "Value": "W/\"UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q9ht133ko3"}, {"Name": "integrity", "Value": "sha256-UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.q9ht133ko3.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10203"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q9ht133ko3"}, {"Name": "integrity", "Value": "sha256-UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.q9ht133ko3.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3250"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AX9UjSDaqolhmzTa/KUDWMjLF2XlYT6I6JZuUGrm1nw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q9ht133ko3"}, {"Name": "integrity", "Value": "sha256-AX9UjSDaqolhmzTa/KUDWMjLF2XlYT6I6JZuUGrm1nw="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.rxsg74s51o.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000294290759"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3397"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ueerZxLdSKIEsWJ13wzBfTcRfd3uGSfOQwhUfM1jRec=\""}, {"Name": "ETag", "Value": "W/\"gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rxsg74s51o"}, {"Name": "integrity", "Value": "sha256-gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.rxsg74s51o.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "12651"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rxsg74s51o"}, {"Name": "integrity", "Value": "sha256-gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.rxsg74s51o.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3397"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ueerZxLdSKIEsWJ13wzBfTcRfd3uGSfOQwhUfM1jRec=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rxsg74s51o"}, {"Name": "integrity", "Value": "sha256-ueerZxLdSKIEsWJ13wzBfTcRfd3uGSfOQwhUfM1jRec="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000082767754"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12081"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VGKPtySribew+qZrTcy+fpnTi7Q3gxSv+HWQQ/4rQos=\""}, {"Name": "ETag", "Value": "W/\"uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "113224"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12081"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VGKPtySribew+qZrTcy+fpnTi7Q3gxSv+HWQQ/4rQos=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VGKPtySribew+qZrTcy+fpnTi7Q3gxSv+HWQQ/4rQos="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022663403"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=\""}, {"Name": "ETag", "Value": "W/\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "267535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.r4e9w2rdcm.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022663403"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=\""}, {"Name": "ETag", "Value": "W/\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4e9w2rdcm"}, {"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.r4e9w2rdcm.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "267535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4e9w2rdcm"}, {"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.r4e9w2rdcm.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4e9w2rdcm"}, {"Name": "integrity", "Value": "sha256-kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.gye83jo8yx.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000082767754"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12081"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VGKPtySribew+qZrTcy+fpnTi7Q3gxSv+HWQQ/4rQos=\""}, {"Name": "ETag", "Value": "W/\"uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gye83jo8yx"}, {"Name": "integrity", "Value": "sha256-uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.gye83jo8yx.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "113224"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gye83jo8yx"}, {"Name": "integrity", "Value": "sha256-uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.gye83jo8yx.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12081"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VGKPtySribew+qZrTcy+fpnTi7Q3gxSv+HWQQ/4rQos=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gye83jo8yx"}, {"Name": "integrity", "Value": "sha256-VGKPtySribew+qZrTcy+fpnTi7Q3gxSv+HWQQ/4rQos="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090350560"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11067"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"cxoqFtEDkm4KCYDrmWgnE7zkiXwSKUlq/rctM76ouDg=\""}, {"Name": "ETag", "Value": "W/\"ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "85357"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.c2oey78nd0.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041081259"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24341"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=\""}, {"Name": "ETag", "Value": "W/\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2oey78nd0"}, {"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.c2oey78nd0.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "180381"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2oey78nd0"}, {"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.c2oey78nd0.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24341"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2oey78nd0"}, {"Name": "integrity", "Value": "sha256-Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11067"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"cxoqFtEDkm4KCYDrmWgnE7zkiXwSKUlq/rctM76ouDg=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cxoqFtEDkm4KCYDrmWgnE7zkiXwSKUlq/rctM76ouDg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041081259"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24341"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=\""}, {"Name": "ETag", "Value": "W/\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "180381"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24341"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.wl58j5mj3v.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090350560"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11067"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"cxoqFtEDkm4KCYDrmWgnE7zkiXwSKUlq/rctM76ouDg=\""}, {"Name": "ETag", "Value": "W/\"ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wl58j5mj3v"}, {"Name": "integrity", "Value": "sha256-ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.wl58j5mj3v.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "85357"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wl58j5mj3v"}, {"Name": "integrity", "Value": "sha256-ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.wl58j5mj3v.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11067"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"cxoqFtEDkm4KCYDrmWgnE7zkiXwSKUlq/rctM76ouDg=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wl58j5mj3v"}, {"Name": "integrity", "Value": "sha256-cxoqFtEDkm4KCYDrmWgnE7zkiXwSKUlq/rctM76ouDg="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083097889"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12033"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rLnVjI3ToFgRadgC+qnQqkjBbeGzyUk/W1M+MRurfiM=\""}, {"Name": "ETag", "Value": "W/\"MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "113083"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12033"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rLnVjI3ToFgRadgC+qnQqkjBbeGzyUk/W1M+MRurfiM=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rLnVjI3ToFgRadgC+qnQqkjBbeGzyUk/W1M+MRurfiM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.j5mq2jizvt.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022677794"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44095"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=\""}, {"Name": "ETag", "Value": "W/\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5mq2jizvt"}, {"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.j5mq2jizvt.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "267476"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5mq2jizvt"}, {"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.j5mq2jizvt.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44095"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5mq2jizvt"}, {"Name": "integrity", "Value": "sha256-KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022677794"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44095"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=\""}, {"Name": "ETag", "Value": "W/\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "267476"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44095"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.d4r6k3f320.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083097889"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12033"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rLnVjI3ToFgRadgC+qnQqkjBbeGzyUk/W1M+MRurfiM=\""}, {"Name": "ETag", "Value": "W/\"MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d4r6k3f320"}, {"Name": "integrity", "Value": "sha256-MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.d4r6k3f320.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "113083"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d4r6k3f320"}, {"Name": "integrity", "Value": "sha256-MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.d4r6k3f320.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12033"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rLnVjI3ToFgRadgC+qnQqkjBbeGzyUk/W1M+MRurfiM=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d4r6k3f320"}, {"Name": "integrity", "Value": "sha256-rLnVjI3ToFgRadgC+qnQqkjBbeGzyUk/W1M+MRurfiM="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090489548"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11050"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Fi8DMQpQ0pHVk8uDPzXmz7v1U9ElGm/J/lTUl54WMHw=\""}, {"Name": "ETag", "Value": "W/\"7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "85286"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11050"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Fi8DMQpQ0pHVk8uDPzXmz7v1U9ElGm/J/lTUl54WMHw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Fi8DMQpQ0pHVk8uDPzXmz7v1U9ElGm/J/lTUl54WMHw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041162427"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24293"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=\""}, {"Name": "ETag", "Value": "W/\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "180217"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24293"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.nvvlpmu67g.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041162427"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24293"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=\""}, {"Name": "ETag", "Value": "W/\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nvvlpmu67g"}, {"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.nvvlpmu67g.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "180217"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nvvlpmu67g"}, {"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.nvvlpmu67g.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24293"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nvvlpmu67g"}, {"Name": "integrity", "Value": "sha256-8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.keugtjm085.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090489548"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11050"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Fi8DMQpQ0pHVk8uDPzXmz7v1U9ElGm/J/lTUl54WMHw=\""}, {"Name": "ETag", "Value": "W/\"7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "keugtjm085"}, {"Name": "integrity", "Value": "sha256-7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.keugtjm085.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "85286"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "keugtjm085"}, {"Name": "integrity", "Value": "sha256-7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.keugtjm085.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11050"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Fi8DMQpQ0pHVk8uDPzXmz7v1U9ElGm/J/lTUl54WMHw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "keugtjm085"}, {"Name": "integrity", "Value": "sha256-Fi8DMQpQ0pHVk8uDPzXmz7v1U9ElGm/J/lTUl54WMHw="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000029833826"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33518"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j8xOcokVqVMlkhtMkAD+dCGjp8eCK+RXUPREaOqe2TU=\""}, {"Name": "ETag", "Value": "W/\"CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "293102"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33518"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j8xOcokVqVMlkhtMkAD+dCGjp8eCK+RXUPREaOqe2TU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j8xOcokVqVMlkhtMkAD+dCGjp8eCK+RXUPREaOqe2TU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008694896"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "115009"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=\""}, {"Name": "ETag", "Value": "W/\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "679755"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "115009"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.pj5nd1wqec.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008694896"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "115009"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=\""}, {"Name": "ETag", "Value": "W/\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pj5nd1wqec"}, {"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.pj5nd1wqec.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "679755"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pj5nd1wqec"}, {"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.pj5nd1wqec.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "115009"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pj5nd1wqec"}, {"Name": "integrity", "Value": "sha256-M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.43atpzeawx.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032290355"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30968"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZA1L2PX1u6VKIuxhJUgeKELAZwIreHb7fDdqlTEZyKs=\""}, {"Name": "ETag", "Value": "W/\"sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "43atpzeawx"}, {"Name": "integrity", "Value": "sha256-sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.43atpzeawx.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "232808"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "43atpzeawx"}, {"Name": "integrity", "Value": "sha256-sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.43atpzeawx.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30968"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZA1L2PX1u6VKIuxhJUgeKELAZwIreHb7fDdqlTEZyKs=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "43atpzeawx"}, {"Name": "integrity", "Value": "sha256-ZA1L2PX1u6VKIuxhJUgeKELAZwIreHb7fDdqlTEZyKs="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032290355"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30968"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZA1L2PX1u6VKIuxhJUgeKELAZwIreHb7fDdqlTEZyKs=\""}, {"Name": "ETag", "Value": "W/\"sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "232808"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30968"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZA1L2PX1u6VKIuxhJUgeKELAZwIreHb7fDdqlTEZyKs=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZA1L2PX1u6VKIuxhJUgeKELAZwIreHb7fDdqlTEZyKs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010892297"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=\""}, {"Name": "ETag", "Value": "W/\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "589892"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.v0zj4ognzu.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010892297"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=\""}, {"Name": "ETag", "Value": "W/\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v0zj4ognzu"}, {"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.v0zj4ognzu.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "589892"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v0zj4ognzu"}, {"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.v0zj4ognzu.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v0zj4ognzu"}, {"Name": "integrity", "Value": "sha256-+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000029962547"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33374"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CKd1DZe0tmjYGXJCdpcyExOT7GvFEZKGtk3+Ij7ZTSc=\""}, {"Name": "ETag", "Value": "W/\"/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "292288"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33374"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CKd1DZe0tmjYGXJCdpcyExOT7GvFEZKGtk3+Ij7ZTSc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CKd1DZe0tmjYGXJCdpcyExOT7GvFEZKGtk3+Ij7ZTSc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.hrwsygsryq.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008699132"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114953"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=\""}, {"Name": "ETag", "Value": "W/\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrwsygsryq"}, {"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.hrwsygsryq.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "679615"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrwsygsryq"}, {"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.hrwsygsryq.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114953"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrwsygsryq"}, {"Name": "integrity", "Value": "sha256-xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008699132"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114953"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=\""}, {"Name": "ETag", "Value": "W/\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "679615"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114953"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.c63t5i9ira.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032266391"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30991"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zxq4fMIq5J99E6EK1H7qVe10DUZ2GcXauY+j6URB7DI=\""}, {"Name": "ETag", "Value": "W/\"rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c63t5i9ira"}, {"Name": "integrity", "Value": "sha256-rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.c63t5i9ira.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "232916"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c63t5i9ira"}, {"Name": "integrity", "Value": "sha256-rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.c63t5i9ira.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30991"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zxq4fMIq5J99E6EK1H7qVe10DUZ2GcXauY+j6URB7DI=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c63t5i9ira"}, {"Name": "integrity", "Value": "sha256-zxq4fMIq5J99E6EK1H7qVe10DUZ2GcXauY+j6URB7DI="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032266391"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30991"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zxq4fMIq5J99E6EK1H7qVe10DUZ2GcXauY+j6URB7DI=\""}, {"Name": "ETag", "Value": "W/\"rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "232916"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.ft3s53vfgj.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010904769"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91702"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=\""}, {"Name": "ETag", "Value": "W/\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ft3s53vfgj"}, {"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.ft3s53vfgj.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "589087"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ft3s53vfgj"}, {"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.ft3s53vfgj.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91702"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ft3s53vfgj"}, {"Name": "integrity", "Value": "sha256-mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30991"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zxq4fMIq5J99E6EK1H7qVe10DUZ2GcXauY+j6URB7DI=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zxq4fMIq5J99E6EK1H7qVe10DUZ2GcXauY+j6URB7DI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010904769"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91702"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=\""}, {"Name": "ETag", "Value": "W/\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "589087"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91702"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.ynyaa8k90p.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000029962547"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33374"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CKd1DZe0tmjYGXJCdpcyExOT7GvFEZKGtk3+Ij7ZTSc=\""}, {"Name": "ETag", "Value": "W/\"/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ynyaa8k90p"}, {"Name": "integrity", "Value": "sha256-/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.ynyaa8k90p.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "292288"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ynyaa8k90p"}, {"Name": "integrity", "Value": "sha256-/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.ynyaa8k90p.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33374"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CKd1DZe0tmjYGXJCdpcyExOT7GvFEZKGtk3+Ij7ZTSc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ynyaa8k90p"}, {"Name": "integrity", "Value": "sha256-CKd1DZe0tmjYGXJCdpcyExOT7GvFEZKGtk3+Ij7ZTSc="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.zub09dkrxp.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000029833826"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33518"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j8xOcokVqVMlkhtMkAD+dCGjp8eCK+RXUPREaOqe2TU=\""}, {"Name": "ETag", "Value": "W/\"CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zub09dkrxp"}, {"Name": "integrity", "Value": "sha256-CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.zub09dkrxp.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "293102"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zub09dkrxp"}, {"Name": "integrity", "Value": "sha256-CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.zub09dkrxp.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33518"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j8xOcokVqVMlkhtMkAD+dCGjp8eCK+RXUPREaOqe2TU=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zub09dkrxp"}, {"Name": "integrity", "Value": "sha256-j8xOcokVqVMlkhtMkAD+dCGjp8eCK+RXUPREaOqe2TU="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.1jo1kezdci.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000025732740"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "38860"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"G3hEVXOXq2YI9Z7/yvAQvvf6j7gWnCvdHiIIKSi826M=\""}, {"Name": "ETag", "Value": "W/\"k9Hcnfw1uBvIz3LBQJE2enuHAQ+2vcGQx7FY/sjMCT0=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1jo1kezdci"}, {"Name": "integrity", "Value": "sha256-k9Hcnfw1uBvIz3LBQJE2enuHAQ+2vcGQx7FY/sjMCT0="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.1jo1kezdci.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "167465"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"k9Hcnfw1uBvIz3LBQJE2enuHAQ+2vcGQx7FY/sjMCT0=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1jo1kezdci"}, {"Name": "integrity", "Value": "sha256-k9Hcnfw1uBvIz3LBQJE2enuHAQ+2vcGQx7FY/sjMCT0="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.1jo1kezdci.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "38860"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"G3hEVXOXq2YI9Z7/yvAQvvf6j7gWnCvdHiIIKSi826M=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1jo1kezdci"}, {"Name": "integrity", "Value": "sha256-G3hEVXOXq2YI9Z7/yvAQvvf6j7gWnCvdHiIIKSi826M="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.aak5eirmym.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000018396895"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "54356"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8xIvF19Nlu+E+UlJdrkdtyfj4s3Cs5JfKiPF/L5QK0c=\""}, {"Name": "ETag", "Value": "W/\"DoRbpT+MFNf8Kw514KCM7HP0Xkm7mSIlvI2H77dfRDA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aak5eirmym"}, {"Name": "integrity", "Value": "sha256-DoRbpT+MFNf8Kw514KCM7HP0Xkm7mSIlvI2H77dfRDA="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.aak5eirmym.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "231707"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"DoRbpT+MFNf8Kw514KCM7HP0Xkm7mSIlvI2H77dfRDA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aak5eirmym"}, {"Name": "integrity", "Value": "sha256-DoRbpT+MFNf8Kw514KCM7HP0Xkm7mSIlvI2H77dfRDA="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.aak5eirmym.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "54356"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8xIvF19Nlu+E+UlJdrkdtyfj4s3Cs5JfKiPF/L5QK0c=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aak5eirmym"}, {"Name": "integrity", "Value": "sha256-8xIvF19Nlu+E+UlJdrkdtyfj4s3Cs5JfKiPF/L5QK0c="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000018396895"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "54356"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8xIvF19Nlu+E+UlJdrkdtyfj4s3Cs5JfKiPF/L5QK0c=\""}, {"Name": "ETag", "Value": "W/\"DoRbpT+MFNf8Kw514KCM7HP0Xkm7mSIlvI2H77dfRDA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DoRbpT+MFNf8Kw514KCM7HP0Xkm7mSIlvI2H77dfRDA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "231707"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"DoRbpT+MFNf8Kw514KCM7HP0Xkm7mSIlvI2H77dfRDA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DoRbpT+MFNf8Kw514KCM7HP0Xkm7mSIlvI2H77dfRDA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "54356"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8xIvF19Nlu+E+UlJdrkdtyfj4s3Cs5JfKiPF/L5QK0c=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8xIvF19Nlu+E+UlJdrkdtyfj4s3Cs5JfKiPF/L5QK0c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.m8m04ow07k.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010863071"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "92054"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"PqV7FoSrUvHZIAI97OB5Du66Rix7At2iTuksxeoo5Gk=\""}, {"Name": "ETag", "Value": "W/\"llRPAjsTCQdX9I6jAgtmhr1rUT9tvAVdaPQrYD38Td4=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m8m04ow07k"}, {"Name": "integrity", "Value": "sha256-llRPAjsTCQdX9I6jAgtmhr1rUT9tvAVdaPQrYD38Td4="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.m8m04ow07k.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "444579"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"llRPAjsTCQdX9I6jAgtmhr1rUT9tvAVdaPQrYD38Td4=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m8m04ow07k"}, {"Name": "integrity", "Value": "sha256-llRPAjsTCQdX9I6jAgtmhr1rUT9tvAVdaPQrYD38Td4="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.m8m04ow07k.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "92054"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"PqV7FoSrUvHZIAI97OB5Du66Rix7At2iTuksxeoo5Gk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m8m04ow07k"}, {"Name": "integrity", "Value": "sha256-PqV7FoSrUvHZIAI97OB5Du66Rix7At2iTuksxeoo5Gk="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010863071"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "92054"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"PqV7FoSrUvHZIAI97OB5Du66Rix7At2iTuksxeoo5Gk=\""}, {"Name": "ETag", "Value": "W/\"llRPAjsTCQdX9I6jAgtmhr1rUT9tvAVdaPQrYD38Td4=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-llRPAjsTCQdX9I6jAgtmhr1rUT9tvAVdaPQrYD38Td4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "444579"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"llRPAjsTCQdX9I6jAgtmhr1rUT9tvAVdaPQrYD38Td4=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-llRPAjsTCQdX9I6jAgtmhr1rUT9tvAVdaPQrYD38Td4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "92054"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"PqV7FoSrUvHZIAI97OB5Du66Rix7At2iTuksxeoo5Gk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PqV7FoSrUvHZIAI97OB5Du66Rix7At2iTuksxeoo5Gk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030426581"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32865"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N4UpPPH7dwqNn708OBuX/liy3v1uuPjjyR9ELovuEYw=\""}, {"Name": "ETag", "Value": "W/\"RCqG8kOYgTkEN6lTuil29qaURdLpMdgr2x1tKpEn2Ow=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RCqG8kOYgTkEN6lTuil29qaURdLpMdgr2x1tKpEn2Ow="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "98317"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RCqG8kOYgTkEN6lTuil29qaURdLpMdgr2x1tKpEn2Ow=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RCqG8kOYgTkEN6lTuil29qaURdLpMdgr2x1tKpEn2Ow="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.07r8e49zdp.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011499540"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "86959"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"S45O8CWj18jAuvMjr7tniBMZLZx6s1gfCjeu1uXTsjQ=\""}, {"Name": "ETag", "Value": "W/\"rOniCgnoNJIVZp7PCJnzSGta8mgTGweOpxp/z4+Oc+k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "07r8e49zdp"}, {"Name": "integrity", "Value": "sha256-rOniCgnoNJIVZp7PCJnzSGta8mgTGweOpxp/z4+Oc+k="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.07r8e49zdp.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "332090"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rOniCgnoNJIVZp7PCJnzSGta8mgTGweOpxp/z4+Oc+k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "07r8e49zdp"}, {"Name": "integrity", "Value": "sha256-rOniCgnoNJIVZp7PCJnzSGta8mgTGweOpxp/z4+Oc+k="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.07r8e49zdp.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "86959"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"S45O8CWj18jAuvMjr7tniBMZLZx6s1gfCjeu1uXTsjQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "07r8e49zdp"}, {"Name": "integrity", "Value": "sha256-S45O8CWj18jAuvMjr7tniBMZLZx6s1gfCjeu1uXTsjQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32865"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N4UpPPH7dwqNn708OBuX/liy3v1uuPjjyR9ELovuEYw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-N4UpPPH7dwqNn708OBuX/liy3v1uuPjjyR9ELovuEYw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011499540"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "86959"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"S45O8CWj18jAuvMjr7tniBMZLZx6s1gfCjeu1uXTsjQ=\""}, {"Name": "ETag", "Value": "W/\"rOniCgnoNJIVZp7PCJnzSGta8mgTGweOpxp/z4+Oc+k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rOniCgnoNJIVZp7PCJnzSGta8mgTGweOpxp/z4+Oc+k="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "332090"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rOniCgnoNJIVZp7PCJnzSGta8mgTGweOpxp/z4+Oc+k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rOniCgnoNJIVZp7PCJnzSGta8mgTGweOpxp/z4+Oc+k="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "86959"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"S45O8CWj18jAuvMjr7tniBMZLZx6s1gfCjeu1uXTsjQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S45O8CWj18jAuvMjr7tniBMZLZx6s1gfCjeu1uXTsjQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.sebe1i1n8x.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030426581"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32865"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N4UpPPH7dwqNn708OBuX/liy3v1uuPjjyR9ELovuEYw=\""}, {"Name": "ETag", "Value": "W/\"RCqG8kOYgTkEN6lTuil29qaURdLpMdgr2x1tKpEn2Ow=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sebe1i1n8x"}, {"Name": "integrity", "Value": "sha256-RCqG8kOYgTkEN6lTuil29qaURdLpMdgr2x1tKpEn2Ow="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.sebe1i1n8x.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "98317"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RCqG8kOYgTkEN6lTuil29qaURdLpMdgr2x1tKpEn2Ow=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sebe1i1n8x"}, {"Name": "integrity", "Value": "sha256-RCqG8kOYgTkEN6lTuil29qaURdLpMdgr2x1tKpEn2Ow="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.sebe1i1n8x.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32865"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N4UpPPH7dwqNn708OBuX/liy3v1uuPjjyR9ELovuEYw=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sebe1i1n8x"}, {"Name": "integrity", "Value": "sha256-N4UpPPH7dwqNn708OBuX/liy3v1uuPjjyR9ELovuEYw="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000026304022"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "38016"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kIioyvmf/tLdemeqRPWlzFgcfoZ1Ove/FdckmYDBXpk=\""}, {"Name": "ETag", "Value": "W/\"V9VHrfWTQdDZhpDjgAzkkk7JIEn/cx3lMtjiDUXul/Q=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V9VHrfWTQdDZhpDjgAzkkk7JIEn/cx3lMtjiDUXul/Q="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "157850"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"V9VHrfWTQdDZhpDjgAzkkk7JIEn/cx3lMtjiDUXul/Q=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V9VHrfWTQdDZhpDjgAzkkk7JIEn/cx3lMtjiDUXul/Q="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "38016"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kIioyvmf/tLdemeqRPWlzFgcfoZ1Ove/FdckmYDBXpk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kIioyvmf/tLdemeqRPWlzFgcfoZ1Ove/FdckmYDBXpk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015593812"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64127"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kXW7yFoYyoJuZ8DvEVLMROrQ/SMEw2Pbr7LLiGIZlCY=\""}, {"Name": "ETag", "Value": "W/\"1NZ0e0qqYzJigO5XFb9Y2bgUwRxi1f/jpwe4eU6A5pA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1NZ0e0qqYzJigO5XFb9Y2bgUwRxi1f/jpwe4eU6A5pA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "305438"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"1NZ0e0qqYzJigO5XFb9Y2bgUwRxi1f/jpwe4eU6A5pA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1NZ0e0qqYzJigO5XFb9Y2bgUwRxi1f/jpwe4eU6A5pA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64127"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kXW7yFoYyoJuZ8DvEVLMROrQ/SMEw2Pbr7LLiGIZlCY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kXW7yFoYyoJuZ8DvEVLMROrQ/SMEw2Pbr7LLiGIZlCY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.sybl3t77fa.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015593812"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64127"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kXW7yFoYyoJuZ8DvEVLMROrQ/SMEw2Pbr7LLiGIZlCY=\""}, {"Name": "ETag", "Value": "W/\"1NZ0e0qqYzJigO5XFb9Y2bgUwRxi1f/jpwe4eU6A5pA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sybl3t77fa"}, {"Name": "integrity", "Value": "sha256-1NZ0e0qqYzJigO5XFb9Y2bgUwRxi1f/jpwe4eU6A5pA="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.sybl3t77fa.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "305438"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"1NZ0e0qqYzJigO5XFb9Y2bgUwRxi1f/jpwe4eU6A5pA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sybl3t77fa"}, {"Name": "integrity", "Value": "sha256-1NZ0e0qqYzJigO5XFb9Y2bgUwRxi1f/jpwe4eU6A5pA="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.sybl3t77fa.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64127"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kXW7yFoYyoJuZ8DvEVLMROrQ/SMEw2Pbr7LLiGIZlCY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sybl3t77fa"}, {"Name": "integrity", "Value": "sha256-kXW7yFoYyoJuZ8DvEVLMROrQ/SMEw2Pbr7LLiGIZlCY="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000035487420"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "28178"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xhw67ZJiHnM6w7VhJZ0xa4j4CdRyJh0f/YiG0s0T63k=\""}, {"Name": "ETag", "Value": "W/\"Rz2BsZTYP8L0/pkMgZIAMFzaFfsMWc+8175AqV69XCE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Rz2BsZTYP8L0/pkMgZIAMFzaFfsMWc+8175AqV69XCE="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "91499"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Rz2BsZTYP8L0/pkMgZIAMFzaFfsMWc+8175AqV69XCE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Rz2BsZTYP8L0/pkMgZIAMFzaFfsMWc+8175AqV69XCE="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "28178"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xhw67ZJiHnM6w7VhJZ0xa4j4CdRyJh0f/YiG0s0T63k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xhw67ZJiHnM6w7VhJZ0xa4j4CdRyJh0f/YiG0s0T63k="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017645398"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "56671"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gOJ+qyInHxVGQAHaRs9ggiXfugMTsR/8thKNQLja68A=\""}, {"Name": "ETag", "Value": "W/\"ygX//9b5IsEzbh1wXO6jfbN5R0YVHtui5Zm2Pxzfows=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ygX//9b5IsEzbh1wXO6jfbN5R0YVHtui5Zm2Pxzfows="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "222455"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ygX//9b5IsEzbh1wXO6jfbN5R0YVHtui5Zm2Pxzfows=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ygX//9b5IsEzbh1wXO6jfbN5R0YVHtui5Zm2Pxzfows="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "56671"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gOJ+qyInHxVGQAHaRs9ggiXfugMTsR/8thKNQLja68A=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gOJ+qyInHxVGQAHaRs9ggiXfugMTsR/8thKNQLja68A="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.u3orf5n9fb.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017645398"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "56671"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gOJ+qyInHxVGQAHaRs9ggiXfugMTsR/8thKNQLja68A=\""}, {"Name": "ETag", "Value": "W/\"ygX//9b5IsEzbh1wXO6jfbN5R0YVHtui5Zm2Pxzfows=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u3orf5n9fb"}, {"Name": "integrity", "Value": "sha256-ygX//9b5IsEzbh1wXO6jfbN5R0YVHtui5Zm2Pxzfows="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.u3orf5n9fb.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "222455"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ygX//9b5IsEzbh1wXO6jfbN5R0YVHtui5Zm2Pxzfows=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u3orf5n9fb"}, {"Name": "integrity", "Value": "sha256-ygX//9b5IsEzbh1wXO6jfbN5R0YVHtui5Zm2Pxzfows="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.u3orf5n9fb.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "56671"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gOJ+qyInHxVGQAHaRs9ggiXfugMTsR/8thKNQLja68A=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u3orf5n9fb"}, {"Name": "integrity", "Value": "sha256-gOJ+qyInHxVGQAHaRs9ggiXfugMTsR/8thKNQLja68A="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.l53j0jwsua.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000035487420"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "28178"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xhw67ZJiHnM6w7VhJZ0xa4j4CdRyJh0f/YiG0s0T63k=\""}, {"Name": "ETag", "Value": "W/\"Rz2BsZTYP8L0/pkMgZIAMFzaFfsMWc+8175AqV69XCE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "l53j0jwsua"}, {"Name": "integrity", "Value": "sha256-Rz2BsZTYP8L0/pkMgZIAMFzaFfsMWc+8175AqV69XCE="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.l53j0jwsua.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "91499"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Rz2BsZTYP8L0/pkMgZIAMFzaFfsMWc+8175AqV69XCE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "l53j0jwsua"}, {"Name": "integrity", "Value": "sha256-Rz2BsZTYP8L0/pkMgZIAMFzaFfsMWc+8175AqV69XCE="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.l53j0jwsua.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "28178"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xhw67ZJiHnM6w7VhJZ0xa4j4CdRyJh0f/YiG0s0T63k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "l53j0jwsua"}, {"Name": "integrity", "Value": "sha256-xhw67ZJiHnM6w7VhJZ0xa4j4CdRyJh0f/YiG0s0T63k="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.xy033osspr.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000026304022"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "38016"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kIioyvmf/tLdemeqRPWlzFgcfoZ1Ove/FdckmYDBXpk=\""}, {"Name": "ETag", "Value": "W/\"V9VHrfWTQdDZhpDjgAzkkk7JIEn/cx3lMtjiDUXul/Q=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xy033osspr"}, {"Name": "integrity", "Value": "sha256-V9VHrfWTQdDZhpDjgAzkkk7JIEn/cx3lMtjiDUXul/Q="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.xy033osspr.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "157850"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"V9VHrfWTQdDZhpDjgAzkkk7JIEn/cx3lMtjiDUXul/Q=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xy033osspr"}, {"Name": "integrity", "Value": "sha256-V9VHrfWTQdDZhpDjgAzkkk7JIEn/cx3lMtjiDUXul/Q="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.xy033osspr.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "38016"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kIioyvmf/tLdemeqRPWlzFgcfoZ1Ove/FdckmYDBXpk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xy033osspr"}, {"Name": "integrity", "Value": "sha256-kIioyvmf/tLdemeqRPWlzFgcfoZ1Ove/FdckmYDBXpk="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000025732740"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "38860"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"G3hEVXOXq2YI9Z7/yvAQvvf6j7gWnCvdHiIIKSi826M=\""}, {"Name": "ETag", "Value": "W/\"k9Hcnfw1uBvIz3LBQJE2enuHAQ+2vcGQx7FY/sjMCT0=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k9Hcnfw1uBvIz3LBQJE2enuHAQ+2vcGQx7FY/sjMCT0="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "167465"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"k9Hcnfw1uBvIz3LBQJE2enuHAQ+2vcGQx7FY/sjMCT0=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k9Hcnfw1uBvIz3LBQJE2enuHAQ+2vcGQx7FY/sjMCT0="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "38860"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"G3hEVXOXq2YI9Z7/yvAQvvf6j7gWnCvdHiIIKSi826M=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G3hEVXOXq2YI9Z7/yvAQvvf6j7gWnCvdHiIIKSi826M="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015520720"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64429"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gQXErDzWkQZSfjEczQ6fcQDBKpT0yn/vH7j1YSexA0c=\""}, {"Name": "ETag", "Value": "W/\"u9e0mnJh0mj/d32Mx6N9Ibj/0+LtjionqG5TWUyotPk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u9e0mnJh0mj/d32Mx6N9Ibj/0+LtjionqG5TWUyotPk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "306606"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"u9e0mnJh0mj/d32Mx6N9Ibj/0+LtjionqG5TWUyotPk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u9e0mnJh0mj/d32Mx6N9Ibj/0+LtjionqG5TWUyotPk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64429"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gQXErDzWkQZSfjEczQ6fcQDBKpT0yn/vH7j1YSexA0c=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gQXErDzWkQZSfjEczQ6fcQDBKpT0yn/vH7j1YSexA0c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.xxsbxg4q4i.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015520720"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64429"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gQXErDzWkQZSfjEczQ6fcQDBKpT0yn/vH7j1YSexA0c=\""}, {"Name": "ETag", "Value": "W/\"u9e0mnJh0mj/d32Mx6N9Ibj/0+LtjionqG5TWUyotPk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xxsbxg4q4i"}, {"Name": "integrity", "Value": "sha256-u9e0mnJh0mj/d32Mx6N9Ibj/0+LtjionqG5TWUyotPk="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.xxsbxg4q4i.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "306606"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"u9e0mnJh0mj/d32Mx6N9Ibj/0+LtjionqG5TWUyotPk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xxsbxg4q4i"}, {"Name": "integrity", "Value": "sha256-u9e0mnJh0mj/d32Mx6N9Ibj/0+LtjionqG5TWUyotPk="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.xxsbxg4q4i.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64429"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gQXErDzWkQZSfjEczQ6fcQDBKpT0yn/vH7j1YSexA0c=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xxsbxg4q4i"}, {"Name": "integrity", "Value": "sha256-gQXErDzWkQZSfjEczQ6fcQDBKpT0yn/vH7j1YSexA0c="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.iug4zs40ca.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038056095"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "26276"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4MKp1Hk7iiMTqFAZoVE172eMhUFb9P2egSWFw3YPP4k=\""}, {"Name": "ETag", "Value": "W/\"tp7hlG6xiG3KkONtF2Z5esr5FTcW2r83xX3hex8WMX8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iug4zs40ca"}, {"Name": "integrity", "Value": "sha256-tp7hlG6xiG3KkONtF2Z5esr5FTcW2r83xX3hex8WMX8="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.iug4zs40ca.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "78199"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tp7hlG6xiG3KkONtF2Z5esr5FTcW2r83xX3hex8WMX8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iug4zs40ca"}, {"Name": "integrity", "Value": "sha256-tp7hlG6xiG3KkONtF2Z5esr5FTcW2r83xX3hex8WMX8="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.iug4zs40ca.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "26276"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4MKp1Hk7iiMTqFAZoVE172eMhUFb9P2egSWFw3YPP4k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iug4zs40ca"}, {"Name": "integrity", "Value": "sha256-4MKp1Hk7iiMTqFAZoVE172eMhUFb9P2egSWFw3YPP4k="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038056095"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "26276"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4MKp1Hk7iiMTqFAZoVE172eMhUFb9P2egSWFw3YPP4k=\""}, {"Name": "ETag", "Value": "W/\"tp7hlG6xiG3KkONtF2Z5esr5FTcW2r83xX3hex8WMX8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tp7hlG6xiG3KkONtF2Z5esr5FTcW2r83xX3hex8WMX8="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "78199"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tp7hlG6xiG3KkONtF2Z5esr5FTcW2r83xX3hex8WMX8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tp7hlG6xiG3KkONtF2Z5esr5FTcW2r83xX3hex8WMX8="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "26276"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4MKp1Hk7iiMTqFAZoVE172eMhUFb9P2egSWFw3YPP4k=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4MKp1Hk7iiMTqFAZoVE172eMhUFb9P2egSWFw3YPP4k="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.knl5pustf3.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017904462"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "55851"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"nCVT1+BjbwsRwzOPpN1ZSa/iEC2jTv4WkLCtAvNJhLY=\""}, {"Name": "ETag", "Value": "W/\"IBIemhxt8rrB7DVR6DoN0KwZECFkw69mvMSn40GJXsc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "knl5pustf3"}, {"Name": "integrity", "Value": "sha256-IBIemhxt8rrB7DVR6DoN0KwZECFkw69mvMSn40GJXsc="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.knl5pustf3.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "220561"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"IBIemhxt8rrB7DVR6DoN0KwZECFkw69mvMSn40GJXsc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "knl5pustf3"}, {"Name": "integrity", "Value": "sha256-IBIemhxt8rrB7DVR6DoN0KwZECFkw69mvMSn40GJXsc="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.knl5pustf3.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "55851"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"nCVT1+BjbwsRwzOPpN1ZSa/iEC2jTv4WkLCtAvNJhLY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "knl5pustf3"}, {"Name": "integrity", "Value": "sha256-nCVT1+BjbwsRwzOPpN1ZSa/iEC2jTv4WkLCtAvNJhLY="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017904462"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "55851"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"nCVT1+BjbwsRwzOPpN1ZSa/iEC2jTv4WkLCtAvNJhLY=\""}, {"Name": "ETag", "Value": "W/\"IBIemhxt8rrB7DVR6DoN0KwZECFkw69mvMSn40GJXsc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IBIemhxt8rrB7DVR6DoN0KwZECFkw69mvMSn40GJXsc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "220561"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"IBIemhxt8rrB7DVR6DoN0KwZECFkw69mvMSn40GJXsc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 08:37:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IBIemhxt8rrB7DVR6DoN0KwZECFkw69mvMSn40GJXsc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "55851"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"nCVT1+BjbwsRwzOPpN1ZSa/iEC2jTv4WkLCtAvNJhLY=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 11:23:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nCVT1+BjbwsRwzOPpN1ZSa/iEC2jTv4WkLCtAvNJhLY="}]}]}